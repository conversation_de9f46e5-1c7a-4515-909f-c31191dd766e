import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { TemplateService } from '../../templates/template.service';
import { PurchaseOrderReceiptData, PurchaseOrderPdfOptions } from '../interfaces/purchase-order-receipt.interface';
import * as puppeteer from 'puppeteer';
import { formatDate } from 'date-fns';
import { id as localeId } from 'date-fns/locale';

@Injectable()
export class PurchaseOrderPdfService {
  constructor(
    private prisma: PrismaService,
    private templateService: TemplateService,
    @InjectPinoLogger(PurchaseOrderPdfService.name)
    private readonly logger: PinoLogger,
  ) {}

  /**
   * Generate purchase order receipt data for PDF generation
   */
  async generatePurchaseOrderReceipt(purchaseOrderId: string): Promise<PurchaseOrderReceiptData> {
    this.logger.trace({ purchaseOrderId }, 'Entering generatePurchaseOrderReceipt method');

    try {
      // Fetch purchase order with all related data
      this.logger.debug({ purchaseOrderId }, 'Fetching purchase order data for receipt generation');
      const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        include: {
          supplier: {
            include: {
              contacts: {
                where: { isActive: true },
                orderBy: [{ isPrimary: 'desc' }, { createdAt: 'asc' }],
              },
            },
          },
          items: {
            include: {
              product: true,
              unit: true,
            },
          },
          createdByUser: true,
        },
      });

      if (!purchaseOrder) {
        this.logger.warn({ purchaseOrderId }, 'Purchase order not found for receipt generation');
        throw new NotFoundException(`Purchase order dengan ID ${purchaseOrderId} tidak ditemukan`);
      }

      this.logger.debug({
        purchaseOrderId,
        orderNumber: purchaseOrder.orderNumber,
        status: purchaseOrder.status,
        supplierId: purchaseOrder.supplierId,
        itemsCount: purchaseOrder.items.length,
        totalAmount: Number(purchaseOrder.totalAmount)
      }, 'Purchase order data retrieved for receipt generation');

    // Get pharmacy/company information (you might want to get this from settings)
    const pharmacyInfo = await this.getPharmacyInfo();

    // Transform data to receipt format
    const receiptData: PurchaseOrderReceiptData = {
      pharmacy: pharmacyInfo,
      purchaseOrder: {
        orderNumber: purchaseOrder.orderNumber,
        orderDate: formatDate(purchaseOrder.orderDate, 'dd MMMM yyyy', { locale: localeId }),
        expectedDelivery: purchaseOrder.expectedDelivery 
          ? formatDate(purchaseOrder.expectedDelivery, 'dd MMMM yyyy', { locale: localeId })
          : undefined,
        status: this.getPurchaseOrderStatusLabel(purchaseOrder.status),
        notes: purchaseOrder.notes || undefined,
        internalNotes: purchaseOrder.internalNotes || undefined,
      },
      supplier: {
        name: purchaseOrder.supplier.name,
        code: purchaseOrder.supplier.code,
        address: purchaseOrder.supplier.address || undefined,
        phone: purchaseOrder.supplier.phone || undefined,
        email: purchaseOrder.supplier.email || undefined,
        contactPerson: purchaseOrder.supplier.contacts?.[0]?.name || undefined,
      },
      items: purchaseOrder.items.map(item => ({
        productCode: item.product.code,
        productName: item.product.name,
        manufacturer: item.product.manufacturer || undefined,
        category: item.product.category || undefined,
        quantityOrdered: item.quantityOrdered,
        unit: item.unit.abbreviation,
        unitPrice: Number(item.unitPrice),
        discountAmount: Number(item.discountAmount),
        totalPrice: Number(item.totalPrice),
        notes: item.notes || undefined,
        expectedDelivery: item.expectedDelivery 
          ? formatDate(new Date(item.expectedDelivery), 'dd MMMM yyyy', { locale: localeId })
          : undefined,
      })),
      totals: {
        subtotal: Number(purchaseOrder.subtotal),
        discountAmount: Number(purchaseOrder.discountAmount),
        taxAmount: Number(purchaseOrder.taxAmount),
        totalAmount: Number(purchaseOrder.totalAmount),
      },
      payment: {
        method: purchaseOrder.paymentMethod || undefined,
        terms: purchaseOrder.paymentTerms || undefined,
      },
      delivery: {
        address: purchaseOrder.deliveryAddress || undefined,
        contact: purchaseOrder.deliveryContact || undefined,
        phone: purchaseOrder.deliveryPhone || undefined,
        notes: purchaseOrder.deliveryNotes || undefined,
      },
      createdBy: {
        name: purchaseOrder.createdByUser
          ? `${purchaseOrder.createdByUser.firstName} ${purchaseOrder.createdByUser.lastName}`.trim()
          : 'System',
        email: purchaseOrder.createdByUser?.email || '',
      },
      footer: {
        notes: 'Dokumen ini dibuat secara otomatis oleh sistem manajemen apotek',
        terms: 'Harap konfirmasi penerimaan purchase order ini dalam 2x24 jam',
      },
    };

    this.logger.info({
      purchaseOrderId,
      orderNumber: purchaseOrder.orderNumber,
      supplierId: purchaseOrder.supplierId,
      itemsCount: receiptData.items.length,
      totalAmount: receiptData.totals.totalAmount,
      status: purchaseOrder.status
    }, 'Purchase order receipt data generated successfully');

    this.logger.trace({ purchaseOrderId }, 'Exiting generatePurchaseOrderReceipt method');
    return receiptData;
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId
      }, 'Failed to generate purchase order receipt data');
      throw error;
    }
  }

  /**
   * Generate HTML from purchase order data using template
   */
  generatePurchaseOrderHtml(receiptData: PurchaseOrderReceiptData): string {
    return this.templateService.generatePurchaseOrderHtml(receiptData);
  }

  /**
   * Generate PDF from purchase order data
   */
  async generatePurchaseOrderPdf(
    purchaseOrderId: string,
    options: PurchaseOrderPdfOptions = {}
  ): Promise<Buffer> {
    this.logger.trace({ purchaseOrderId, options }, 'Entering generatePurchaseOrderPdf method');

    try {
      this.logger.debug({ purchaseOrderId }, 'Generating purchase order receipt data for PDF');
      const receiptData = await this.generatePurchaseOrderReceipt(purchaseOrderId);

      this.logger.debug({
        purchaseOrderId,
        orderNumber: receiptData.purchaseOrder.orderNumber,
        format: options.format || 'A4',
        orientation: options.orientation || 'portrait'
      }, 'Converting receipt data to HTML');

      const html = this.generatePurchaseOrderHtml(receiptData);

      this.logger.debug({ purchaseOrderId }, 'Launching Puppeteer browser for PDF generation');
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      try {
        const page = await browser.newPage();

        this.logger.debug({ purchaseOrderId }, 'Setting HTML content in browser page');
        await page.setContent(html, { waitUntil: 'networkidle0' });

        const pdfOptions = {
          format: options.format || 'A4' as const,
          orientation: options.orientation || 'portrait' as const,
          margin: {
            top: '15mm',
            right: '15mm',
            bottom: '15mm',
            left: '15mm'
          },
          printBackground: true,
          preferCSSPageSize: false,
        };

        this.logger.debug({
          purchaseOrderId,
          format: pdfOptions.format,
          orientation: pdfOptions.orientation
        }, 'Generating PDF with specified options');

        const pdfBuffer = await page.pdf(pdfOptions);
        const buffer = Buffer.from(pdfBuffer);

        this.logger.info({
          purchaseOrderId,
          orderNumber: receiptData.purchaseOrder.orderNumber,
          format: pdfOptions.format,
          orientation: pdfOptions.orientation,
          pdfSizeBytes: buffer.length
        }, 'Purchase order PDF generated successfully');

        this.logger.trace({ purchaseOrderId }, 'Exiting generatePurchaseOrderPdf method');
        return buffer;
      } finally {
        this.logger.debug({ purchaseOrderId }, 'Closing Puppeteer browser');
        await browser.close();
      }
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId,
        options
      }, 'Failed to generate purchase order PDF');
      throw error;
    }
  }

  /**
   * Get pharmacy/company information
   * This should ideally come from a settings service or configuration
   */
  private async getPharmacyInfo() {
    // For now, return default pharmacy info
    // In a real implementation, this would come from a settings table or configuration
    return {
      name: 'Apotek Sehat Bersama',
      address: 'Jl. Kesehatan No. 123, Jakarta Pusat 10110',
      phone: '(021) 1234-5678',
      email: '<EMAIL>',
      license: 'SIPA.123.456.789',
    };
  }

  /**
   * Get human-readable status label
   */
  private getPurchaseOrderStatusLabel(status: string): string {
    const statusLabels: Record<string, string> = {
      DRAFT: 'Draft',
      PENDING_APPROVAL: 'Menunggu Persetujuan',
      APPROVED: 'Disetujui',
      SUBMITTED: 'Dikirim ke Supplier',
      ORDERED: 'Dipesan',
      PARTIALLY_RECEIVED: 'Diterima Sebagian',
      FULLY_RECEIVED: 'Diterima Lengkap',
      CANCELLED: 'Dibatalkan',
      CLOSED: 'Ditutup',
    };

    return statusLabels[status] || status;
  }
}
