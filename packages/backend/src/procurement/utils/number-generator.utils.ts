import { Injectable } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { NumberGenerationOptions, GeneratedNumber } from '../interfaces/procurement.interface';

@Injectable()
export class NumberGeneratorService {
  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(NumberGeneratorService.name)
    private readonly logger: PinoLogger,
  ) { }

  /**
   * Generates a unique purchase order number
   * Format: PO-YYYYMMDD-XXX
   */
  async generatePurchaseOrderNumber(): Promise<string> {
    this.logger.trace('Entering generatePurchaseOrderNumber method');

    const options: NumberGenerationOptions = {
      prefix: 'PO',
      dateFormat: 'YYYYMMDD',
      sequenceLength: 3
    };

    this.logger.debug({
      prefix: options.prefix,
      dateFormat: options.dateFormat,
      sequenceLength: options.sequenceLength
    }, 'Generating purchase order number with options');

    try {
      const result = await this.generateNumber(options, async (pattern) => {
        this.logger.debug({ pattern }, 'Searching for existing purchase orders with pattern');

        const existingOrder = await this.prisma.purchaseOrder.findFirst({
          where: {
            orderNumber: {
              startsWith: pattern
            }
          },
          orderBy: {
            orderNumber: 'desc'
          }
        });

        this.logger.debug({
          pattern,
          existingOrderNumber: existingOrder?.orderNumber,
          found: !!existingOrder
        }, 'Existing purchase order search completed');

        return existingOrder?.orderNumber || null;
      });

      this.logger.info({
        generatedNumber: result.number,
        sequence: result.sequence
      }, 'Purchase order number generated successfully');

      this.logger.trace({ number: result.number }, 'Exiting generatePurchaseOrderNumber method');
      return result.number;
    } catch (error) {
      this.logger.error({
        err: error,
        options
      }, 'Failed to generate purchase order number');
      throw error;
    }
  }

  /**
   * Generates a unique goods receipt number
   * Format: GR-YYYYMMDD-XXX
   */
  async generateGoodsReceiptNumber(): Promise<string> {
    const options: NumberGenerationOptions = {
      prefix: 'GR',
      dateFormat: 'YYYYMMDD',
      sequenceLength: 3
    };

    const result = await this.generateNumber(options, async (pattern) => {
      const existingReceipt = await this.prisma.goodsReceipt.findFirst({
        where: {
          receiptNumber: {
            startsWith: pattern
          }
        },
        orderBy: {
          receiptNumber: 'desc'
        }
      });

      return existingReceipt?.receiptNumber || null;
    });

    return result.number;
  }

  /**
   * Generic number generation utility
   * @param options Number generation options
   * @param findLastNumber Function to find the last generated number with the same pattern
   * @returns Generated number with sequence
   */
  private async generateNumber(
    options: NumberGenerationOptions,
    findLastNumber: (pattern: string) => Promise<string | null>
  ): Promise<GeneratedNumber> {
    const { prefix, dateFormat = 'YYYYMMDD', sequenceLength = 3 } = options;

    // Generate date part
    const now = new Date();
    const datePart = this.formatDate(now, dateFormat);

    // Create pattern for searching
    const pattern = `${prefix}-${datePart}`;

    // Find the last number with this pattern
    const lastNumber = await findLastNumber(pattern);

    let sequence = 1;

    if (lastNumber) {
      // Extract sequence from last number
      const lastSequencePart = lastNumber.split('-').pop();
      if (lastSequencePart) {
        const lastSequence = parseInt(lastSequencePart, 10);
        if (!isNaN(lastSequence)) {
          sequence = lastSequence + 1;
        }
      }
    }

    // Format sequence with leading zeros
    const sequencePart = sequence.toString().padStart(sequenceLength, '0');

    // Generate final number
    const number = `${pattern}-${sequencePart}`;

    return {
      number,
      sequence
    };
  }

  /**
   * Formats date according to the specified format
   * @param date Date to format
   * @param format Format string (YYYY, MM, DD, etc.)
   * @returns Formatted date string
   */
  private formatDate(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day);
  }

  /**
   * Validates if a number follows the expected format
   * @param number Number to validate
   * @param prefix Expected prefix
   * @returns boolean indicating if number is valid
   */
  static validateNumberFormat(number: string, prefix: string): boolean {
    if (!number || typeof number !== 'string') {
      return false;
    }

    // Expected format: PREFIX-YYYYMMDD-XXX
    const pattern = new RegExp(`^${prefix}-\\d{8}-\\d{3}$`);
    return pattern.test(number);
  }

  /**
   * Extracts date from a generated number
   * @param number Generated number
   * @returns Date object or null if invalid
   */
  static extractDateFromNumber(number: string): Date | null {
    if (!number || typeof number !== 'string') {
      return null;
    }

    const parts = number.split('-');
    if (parts.length !== 3) {
      return null;
    }

    const datePart = parts[1];
    if (datePart.length !== 8) {
      return null;
    }

    const year = parseInt(datePart.substring(0, 4), 10);
    const month = parseInt(datePart.substring(4, 6), 10) - 1; // Month is 0-indexed
    const day = parseInt(datePart.substring(6, 8), 10);

    const date = new Date(year, month, day);

    // Validate the date
    if (date.getFullYear() !== year ||
      date.getMonth() !== month ||
      date.getDate() !== day) {
      return null;
    }

    return date;
  }

  /**
   * Extracts sequence number from a generated number
   * @param number Generated number
   * @returns Sequence number or null if invalid
   */
  static extractSequenceFromNumber(number: string): number | null {
    if (!number || typeof number !== 'string') {
      return null;
    }

    const parts = number.split('-');
    if (parts.length !== 3) {
      return null;
    }

    const sequencePart = parts[2];
    const sequence = parseInt(sequencePart, 10);

    return isNaN(sequence) ? null : sequence;
  }
}
