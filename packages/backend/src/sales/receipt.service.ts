import { Injectable, NotFoundException } from '@nestjs/common';
import { Inject<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Logger } from 'nestjs-pino';
import { PrismaService } from '../prisma/prisma.service';
import { PharmacySettingsService } from '../pharmacy/pharmacy-settings.service';
import { TemplateService } from '../templates/template.service';
import * as puppeteer from 'puppeteer';

export interface ReceiptData {
  // Header Information
  pharmacy: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    license?: string;
  };

  // Transaction Information
  transaction: {
    saleNumber: string;
    date: string;
    time: string;
    cashier: {
      name: string;
      id: string;
    };
  };

  // Customer Information
  customer: {
    name: string;
    phone?: string;
    type: 'WALK_IN' | 'REGISTERED';
    membershipNumber?: string;
  };

  // Items
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    unit: string;
    unitPrice: number;
    discount: number;
    subtotal: number;
    manufacturer?: string;
  }>;

  // Totals
  totals: {
    subtotal: number;
    discount: number;
    afterDiscount: number;
    tax: number;
    total: number;
  };

  // Payment Information
  payment: {
    method: string;
    amountPaid: number;
    change: number;
  };

  // Footer Information
  footer: {
    thankYouMessage: string;
    returnPolicy: string;
    notes?: string;
  };

  // Thermal Printer Formatting Hints
  formatting: {
    paperWidth: 58 | 80; // mm
    lineBreaks: {
      afterHeader: number;
      afterTransaction: number;
      afterCustomer: number;
      afterItems: number;
      afterTotals: number;
      afterPayment: number;
      beforeFooter: number;
    };
    alignment: {
      header: 'center' | 'left' | 'right';
      items: 'left' | 'right';
      totals: 'right';
      footer: 'center';
    };
    fontSizes: {
      header: 'normal' | 'large' | 'small';
      items: 'normal' | 'small';
      totals: 'normal' | 'large';
      footer: 'small';
    };
  };
}

@Injectable()
export class ReceiptService {
  constructor(
    private prisma: PrismaService,
    private pharmacySettingsService: PharmacySettingsService,
    private templateService: TemplateService,
    @InjectPinoLogger(ReceiptService.name)
    private readonly logger: PinoLogger,
  ) { }

  /**
   * Generate receipt data for a completed sale
   */
  async generateReceipt(saleId: string): Promise<ReceiptData> {
    this.logger.trace({ saleId }, 'Entering generateReceipt method');

    try {
      // Get sale with all related data
      this.logger.debug({ saleId }, 'Fetching sale data for receipt generation');
      const sale = await this.prisma.sale.findUnique({
        where: { id: saleId },
        include: {
          customer: true,
          cashier: true,
          saleItems: {
            include: {
              product: true,
              unit: true,
            },
          },
        },
      });

      if (!sale) {
        this.logger.warn({ saleId }, 'Sale not found for receipt generation');
        throw new NotFoundException('Transaksi tidak ditemukan');
      }

      this.logger.debug({
        saleId,
        saleNumber: sale.saleNumber,
        status: sale.status,
        itemsCount: sale.saleItems.length,
        totalAmount: Number(sale.totalAmount)
      }, 'Sale data retrieved for receipt generation');

      // Get pharmacy information (in a real system, this would come from settings)
      this.logger.debug({ saleId }, 'Fetching pharmacy information');
      const pharmacyInfo = await this.getPharmacyInfo();

    // Format date and time
    const saleDate = new Date(sale.saleDate);
    const formattedDate = saleDate.toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
    const formattedTime = saleDate.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });

    // Format customer information
    const customer = {
      name: sale.customerName || 'Walk-in Customer',
      phone: sale.customerPhone || undefined,
      type: sale.customerId ? 'REGISTERED' as const : 'WALK_IN' as const,
      membershipNumber: sale.customer?.code,
    };

    // Format items
    const items = sale.saleItems.map(item => ({
      name: item.product.name,
      code: item.product.code,
      quantity: item.quantity,
      unit: item.unit.abbreviation,
      unitPrice: Number(item.unitPrice),
      discount: Number(item.discountAmount),
      subtotal: Number(item.totalPrice),
      manufacturer: item.product.manufacturer || undefined,
    }));

    // Calculate totals
    const itemsSubtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
    const itemsDiscount = items.reduce((sum, item) => sum + item.discount, 0);
    const saleDiscount = Number(sale.discountAmount);
    const totalDiscount = itemsDiscount + saleDiscount;

    const totals = {
      subtotal: itemsSubtotal,
      discount: totalDiscount,
      afterDiscount: Number(sale.subtotal),
      tax: Number(sale.taxAmount),
      total: Number(sale.totalAmount),
    };

    // Format payment information
    const payment = {
      method: this.formatPaymentMethod(sale.paymentMethod),
      amountPaid: Number(sale.amountPaid),
      change: Number(sale.changeAmount),
    };

    // Generate receipt data
    this.logger.debug({
      saleId,
      saleNumber: sale.saleNumber,
      itemsCount: items.length,
      totalAmount: totals.total,
      paymentMethod: payment.method
    }, 'Generating receipt data structure');

    const receiptData: ReceiptData = {
      pharmacy: pharmacyInfo,
      transaction: {
        saleNumber: sale.saleNumber,
        date: formattedDate,
        time: formattedTime,
        cashier: {
          name: `${sale.cashier.firstName} ${sale.cashier.lastName}`,
          id: sale.cashier.id,
        },
      },
      customer,
      items,
      totals,
      payment,
      footer: {
        thankYouMessage: 'Terima kasih atas kunjungan Anda',
        returnPolicy: 'Barang yang sudah dibeli tidak dapat dikembalikan kecuali ada kesalahan dari pihak apotek',
        notes: sale.notes || undefined,
      },
      formatting: this.getThermalPrinterFormatting(),
    };

    this.logger.info({
      saleId,
      saleNumber: sale.saleNumber,
      customerType: customer.type,
      itemsCount: items.length,
      totalAmount: totals.total,
      paperWidth: receiptData.formatting.paperWidth
    }, 'Receipt data generated successfully');

    this.logger.trace({ saleId }, 'Exiting generateReceipt method');
    return receiptData;
    } catch (error) {
      this.logger.error({
        err: error,
        saleId
      }, 'Failed to generate receipt data');
      throw error;
    }
  }

  /**
   * Get pharmacy information from settings
   */
  private async getPharmacyInfo() {
    return this.pharmacySettingsService.getPharmacyInfo();
  }

  /**
   * Format payment method for display
   */
  private formatPaymentMethod(method: string): string {
    const methodMap: Record<string, string> = {
      CASH: 'Tunai',
      TRANSFER: 'Transfer Bank',
      CREDIT: 'Kartu Kredit',
      GIRO: 'Giro',
    };
    return methodMap[method] || method;
  }

  /**
   * Get thermal printer formatting configuration
   */
  private getThermalPrinterFormatting() {
    return {
      paperWidth: 58 as const, // 58mm thermal paper (most common)
      lineBreaks: {
        afterHeader: 2,
        afterTransaction: 1,
        afterCustomer: 1,
        afterItems: 2,
        afterTotals: 1,
        afterPayment: 2,
        beforeFooter: 1,
      },
      alignment: {
        header: 'center' as const,
        items: 'left' as const,
        totals: 'right' as const,
        footer: 'center' as const,
      },
      fontSizes: {
        header: 'normal' as const,
        items: 'small' as const,
        totals: 'normal' as const,
        footer: 'small' as const,
      },
    };
  }

  /**
   * Generate receipt for 80mm thermal paper
   */
  async generateReceiptWide(saleId: string): Promise<ReceiptData> {
    const receipt = await this.generateReceipt(saleId);

    // Modify formatting for 80mm paper
    receipt.formatting.paperWidth = 80;
    receipt.formatting.fontSizes.header = 'large';
    receipt.formatting.fontSizes.totals = 'large';

    return receipt;
  }

  /**
   * Validate that receipt can be generated for this sale
   */
  async validateReceiptGeneration(saleId: string): Promise<{ canGenerate: boolean; reason?: string }> {
    this.logger.trace({ saleId }, 'Entering validateReceiptGeneration method');

    try {
      const sale = await this.prisma.sale.findUnique({
        where: { id: saleId },
      });

      if (!sale) {
        this.logger.warn({ saleId }, 'Receipt validation failed: sale not found');
        return { canGenerate: false, reason: 'Transaksi tidak ditemukan' };
      }

      this.logger.debug({
        saleId,
        saleNumber: sale.saleNumber,
        status: sale.status
      }, 'Validating sale status for receipt generation');

      if (sale.status === 'DRAFT') {
        this.logger.warn({
          saleId,
          saleNumber: sale.saleNumber,
          status: sale.status
        }, 'Receipt validation failed: sale is in draft status');
        return { canGenerate: false, reason: 'Tidak dapat membuat struk untuk transaksi draft' };
      }

      if (sale.status === 'CANCELLED') {
        this.logger.warn({
          saleId,
          saleNumber: sale.saleNumber,
          status: sale.status
        }, 'Receipt validation failed: sale is cancelled');
        return { canGenerate: false, reason: 'Tidak dapat membuat struk untuk transaksi yang dibatalkan' };
      }

      this.logger.debug({
        saleId,
        saleNumber: sale.saleNumber,
        status: sale.status
      }, 'Receipt validation passed');

      this.logger.trace({ saleId }, 'Exiting validateReceiptGeneration method');
      return { canGenerate: true };
    } catch (error) {
      this.logger.error({
        err: error,
        saleId
      }, 'Error during receipt validation');
      return { canGenerate: false, reason: 'Transaksi tidak ditemukan' };
    }
  }

  /**
   * Generate HTML from receipt data using template
   */
  generateReceiptHtml(receiptData: ReceiptData): string {
    return this.templateService.generateReceiptHtml(receiptData);
  }

  /**
   * Generate PDF receipt from HTML template
   */
  async generateReceiptPdf(saleId: string, isWide: boolean = false): Promise<Buffer> {
    this.logger.trace({ saleId, isWide }, 'Entering generateReceiptPdf method');

    try {
      this.logger.debug({ saleId, isWide }, 'Generating receipt data for PDF');
      const receiptData = isWide
        ? await this.generateReceiptWide(saleId)
        : await this.generateReceipt(saleId);

      this.logger.debug({
        saleId,
        saleNumber: receiptData.transaction.saleNumber,
        paperWidth: receiptData.formatting.paperWidth,
        itemsCount: receiptData.items.length
      }, 'Converting receipt data to HTML');

      const html = this.generateReceiptHtml(receiptData);

      this.logger.debug({ saleId }, 'Launching Puppeteer browser for PDF generation');
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      try {
        const page = await browser.newPage();

        this.logger.debug({ saleId }, 'Setting HTML content in browser page');
        await page.setContent(html, { waitUntil: 'networkidle0' });

        // Calculate appropriate height based on content and paper width
        const paperWidth = isWide ? 80 : 58;
        const estimatedHeight = Math.max(200, receiptData.items.length * 15 + 150); // Dynamic height based on content

        this.logger.debug({
          saleId,
          paperWidth: `${paperWidth}mm`,
          estimatedHeight: `${estimatedHeight}mm`,
          itemsCount: receiptData.items.length
        }, 'Generating PDF with calculated dimensions');

        const pdfBuffer = await page.pdf({
          width: `${paperWidth}mm`,
          height: `${estimatedHeight}mm`,
          margin: {
            top: '2mm',
            right: '2mm',
            bottom: '2mm',
            left: '2mm'
          },
          printBackground: true,
          preferCSSPageSize: false, // Disable CSS page size to use our custom dimensions
          format: undefined // Don't use predefined format, use custom width/height
        });

        const buffer = Buffer.from(pdfBuffer);

        this.logger.info({
          saleId,
          saleNumber: receiptData.transaction.saleNumber,
          paperWidth: `${paperWidth}mm`,
          pdfSizeBytes: buffer.length,
          isWide
        }, 'PDF receipt generated successfully');

        this.logger.trace({ saleId }, 'Exiting generateReceiptPdf method');
        return buffer;
      } finally {
        this.logger.debug({ saleId }, 'Closing Puppeteer browser');
        await browser.close();
      }
    } catch (error) {
      this.logger.error({
        err: error,
        saleId,
        isWide
      }, 'Failed to generate PDF receipt');
      throw error;
    }
  }
}
