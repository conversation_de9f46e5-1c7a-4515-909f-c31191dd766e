import { Injectable, BadRequestException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ogger, InjectPinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { StockMovementType } from '@prisma/client';
import {
  InventoryReportRequestDto,
  InventoryReportResponse,
  InventoryReportType,
  ReportFormat,
  ReportLanguage,
  ReportFiltersDto,
  ImportInventoryDto,
  ImportInventoryResponse,
} from '../dto/inventory-report.dto';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as PDFDocument from 'pdfkit';
import * as XLSX from 'xlsx';
import * as csv from 'csv-parser';

@Injectable()
export class InventoryReportService {
  private readonly reportsDir = path.join(process.cwd(), 'temp', 'reports');

  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(InventoryReportService.name)
    private readonly logger: PinoLogger,
  ) {
    this.logger.trace('Initializing InventoryReportService');

    // Ensure reports directory exists
    if (!fs.existsSync(this.reportsDir)) {
      this.logger.debug({ reportsDir: this.reportsDir }, 'Creating reports directory');
      fs.mkdirSync(this.reportsDir, { recursive: true });
      this.logger.info({ reportsDir: this.reportsDir }, 'Reports directory created');
    } else {
      this.logger.debug({ reportsDir: this.reportsDir }, 'Reports directory already exists');
    }
  }

  async generateReport(request: InventoryReportRequestDto): Promise<InventoryReportResponse> {
    const reportId = uuidv4();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    this.logger.trace({ reportId, type: request.type, format: request.format }, 'Entering generateReport method');

    this.logger.debug({
      reportId,
      type: request.type,
      format: request.format,
      language: request.language,
      includeDetails: request.includeDetails,
      includeSummary: request.includeSummary,
      includeCharts: request.includeCharts,
      userId: request.userInfo?.id,
      userName: request.userInfo?.name,
      pharmacyName: request.pharmacyInfo?.name,
      filters: {
        startDate: request.filters.startDate,
        endDate: request.filters.endDate,
        productId: request.filters.productId,
        supplierId: request.filters.supplierId,
        location: request.filters.location,
        lowStockOnly: request.filters.lowStockOnly,
        expiredOnly: request.filters.expiredOnly,
        expiringSoon: request.filters.expiringSoon,
        expiringSoonDays: request.filters.expiringSoonDays
      }
    }, 'Processing inventory report generation request');

    try {
      let fileName: string;
      let fileContent: Buffer;

      // Validate request parameters
      this.validateReportRequest(request);

      // Get report data based on type
      const reportData = await this.getReportData(request.type, request.filters);

      // Check if data is empty
      if (!reportData || (reportData.items && reportData.items.length === 0)) {
        this.logger.warn(`No data found for report type: ${request.type}`);
      }

      const reportPrefix = request.language === ReportLanguage.EN ? 'Report' : 'Laporan';

      switch (request.format) {
        case ReportFormat.PDF:
          fileName = `${reportPrefix}_${this.getReportTypeName(request.type, request.language)}_${timestamp}.pdf`;
          fileContent = await this.generatePDFReport(request, reportData, reportId);
          break;
        case ReportFormat.EXCEL:
          fileName = `${reportPrefix}_${this.getReportTypeName(request.type, request.language)}_${timestamp}.xlsx`;
          fileContent = await this.generateExcelReport(request, reportData, reportId);
          break;
        case ReportFormat.CSV:
          fileName = `${reportPrefix}_${this.getReportTypeName(request.type, request.language)}_${timestamp}.csv`;
          fileContent = await this.generateCSVReport(request, reportData, reportId);
          break;
        default:
          throw new BadRequestException('Format laporan tidak didukung');
      }

      // Ensure reports directory exists
      if (!fs.existsSync(this.reportsDir)) {
        fs.mkdirSync(this.reportsDir, { recursive: true });
      }

      // Save file to temporary directory
      const filePath = path.join(this.reportsDir, fileName);
      fs.writeFileSync(filePath, fileContent);

      // Calculate expiration time (1 hour from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);

      // Create download URL
      const reportUrl = `/api/reports/download/${fileName}`;

      this.logger.info(`Report generated successfully: ${fileName} (${fileContent.length} bytes)`);

      return {
        reportUrl,
        reportId,
        fileName,
        fileSize: fileContent.length,
        expiresAt: expiresAt.toISOString(),
        format: request.format,
        type: request.type,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error({ err: error }, `Failed to generate report: ${error.message}`);
      throw new BadRequestException(`Gagal membuat laporan: ${error.message}`);
    }
  }

  private validateReportRequest(request: InventoryReportRequestDto): void {
    // Validate date ranges
    if (request.filters.startDate && request.filters.endDate) {
      const startDate = new Date(request.filters.startDate);
      const endDate = new Date(request.filters.endDate);

      if (startDate > endDate) {
        throw new BadRequestException('Tanggal mulai tidak boleh lebih besar dari tanggal akhir');
      }
    }

    // Validate start date separately (can be provided without end date)
    if (request.filters.startDate) {
      const startDate = new Date(request.filters.startDate);
      if (startDate > new Date()) {
        throw new BadRequestException('Tanggal mulai tidak boleh di masa depan');
      }
    }

    // Validate quantity ranges
    if (request.filters.minQuantity !== undefined && request.filters.maxQuantity !== undefined) {
      if (request.filters.minQuantity > request.filters.maxQuantity) {
        throw new BadRequestException('Jumlah minimum tidak boleh lebih besar dari jumlah maksimum');
      }
    }

    // Validate value ranges
    if (request.filters.minValue !== undefined && request.filters.maxValue !== undefined) {
      if (request.filters.minValue > request.filters.maxValue) {
        throw new BadRequestException('Nilai minimum tidak boleh lebih besar dari nilai maksimum');
      }
    }

    // Validate expiry days
    if (request.filters.expiringSoonDays !== undefined) {
      if (request.filters.expiringSoonDays < 1 || request.filters.expiringSoonDays > 365) {
        throw new BadRequestException('Hari kedaluwarsa harus antara 1-365 hari');
      }
    }
  }

  private async getReportData(type: InventoryReportType, filters: ReportFiltersDto): Promise<any> {
    const where = this.buildWhereClause(filters);

    switch (type) {
      case InventoryReportType.STOCK_LEVELS:
        return this.getStockLevelsData(where);
      case InventoryReportType.STOCK_MOVEMENTS:
        return this.getStockMovementsData(where, filters);
      case InventoryReportType.LOW_STOCK:
        return this.getLowStockData(where);
      case InventoryReportType.EXPIRY_REPORT:
        return this.getExpiryReportData(where, filters);
      case InventoryReportType.ALLOCATION_HISTORY:
        return this.getAllocationHistoryData(filters);
      case InventoryReportType.SUPPLIER_PERFORMANCE:
        return this.getSupplierPerformanceData(where, filters);
      case InventoryReportType.CATEGORY_ANALYSIS:
        return this.getCategoryAnalysisData(where);
      case InventoryReportType.STOCK_AGING:
        return this.getStockAgingData(where);
      case InventoryReportType.TURNOVER_ANALYSIS:
        return this.getTurnoverAnalysisData(where, filters);
      default:
        throw new BadRequestException('Jenis laporan tidak didukung');
    }
  }

  private buildWhereClause(filters: ReportFiltersDto): any {
    const where: any = {};

    if (filters.productId) {
      where.productId = filters.productId;
    }

    if (filters.supplierId) {
      where.supplierId = filters.supplierId;
    }

    if (filters.location) {
      where.location = { contains: filters.location, mode: 'insensitive' };
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters.lowStockOnly) {
      where.quantityOnHand = { lte: 10 }; // Low stock threshold
    }

    if (filters.expiredOnly) {
      where.expiryDate = { lte: new Date() };
    }

    if (filters.expiringSoon) {
      const days = filters.expiringSoonDays || 30;
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + days);
      where.expiryDate = {
        gte: new Date(),
        lte: futureDate,
      };
    }

    if (filters.minQuantity !== undefined || filters.maxQuantity !== undefined) {
      where.quantityOnHand = {};
      if (filters.minQuantity !== undefined) {
        where.quantityOnHand.gte = filters.minQuantity;
      }
      if (filters.maxQuantity !== undefined) {
        where.quantityOnHand.lte = filters.maxQuantity;
      }
    }

    if (filters.minValue !== undefined || filters.maxValue !== undefined) {
      where.costPrice = {};
      if (filters.minValue !== undefined) {
        where.costPrice.gte = filters.minValue;
      }
      if (filters.maxValue !== undefined) {
        where.costPrice.lte = filters.maxValue;
      }
    }

    return where;
  }

  private async getStockLevelsData(where: any): Promise<any> {
    try {
      // Get configurable low stock threshold (default to 10)
      const lowStockThreshold = await this.getLowStockThreshold();

      const inventoryItems = await this.prisma.inventoryItem.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              code: true,
              category: true,
              type: true,
            },
          },
          unit: {
            select: {
              id: true,
              name: true,
              abbreviation: true,
            },
          },
          supplier: {
            select: {
              id: true,
              name: true,
              code: true,
              type: true,
            },
          },
        },
        orderBy: [
          { product: { name: 'asc' } },
          { expiryDate: 'asc' },
        ],
      });

      const now = new Date();
      const summary = {
        totalItems: inventoryItems.length,
        totalQuantity: inventoryItems.reduce((sum, item) => sum + item.quantityOnHand, 0),
        totalValue: inventoryItems.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0),
        lowStockItems: inventoryItems.filter(item => item.quantityOnHand <= lowStockThreshold).length,
        expiredItems: inventoryItems.filter(item => item.expiryDate && item.expiryDate <= now).length,
        activeItems: inventoryItems.filter(item => item.isActive).length,
        inactiveItems: inventoryItems.filter(item => !item.isActive).length,
        averageValue: inventoryItems.length > 0
          ? inventoryItems.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0) / inventoryItems.length
          : 0,
        lowStockThreshold,
      };

      return {
        items: inventoryItems,
        summary,
        type: 'stock_levels',
        generatedAt: now.toISOString(),
      };
    } catch (error) {
      this.logger.error({ err: error }, `Error getting stock levels data: ${error.message}`);
      throw new Error(`Gagal mengambil data tingkat stok: ${error.message}`);
    }
  }

  private async getLowStockThreshold(): Promise<number> {
    try {
      // Try to get from settings table, fallback to default
      const setting = await this.prisma.appSettings.findFirst({
        where: { settingKey: 'low_stock_threshold' },
      });
      return setting ? parseInt(setting.settingValue || '10') : 10;
    } catch (error) {
      // If settings table doesn't exist or error, use default
      return 10;
    }
  }

  private async getStockMovementsData(where: any, filters: ReportFiltersDto): Promise<any> {
    // Get inventory items first
    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where,
      select: { id: true },
    });

    const movementWhere: any = {
      inventoryItemId: { in: inventoryItems.map(item => item.id) },
    };

    if (filters.startDate || filters.endDate) {
      movementWhere.createdAt = {};
      if (filters.startDate) {
        movementWhere.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        const endDate = new Date(filters.endDate);
        endDate.setHours(23, 59, 59, 999);
        movementWhere.createdAt.lte = endDate;
      }
    }

    const movements = await this.prisma.stockMovement.findMany({
      where: movementWhere,
      include: {
        inventoryItem: {
          include: {
            product: true,
            unit: true,
            supplier: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    const summary = {
      totalMovements: movements.length,
      inMovements: movements.filter(m => m.quantity > 0).length,
      outMovements: movements.filter(m => m.quantity < 0).length,
      totalInQuantity: movements.filter(m => m.quantity > 0).reduce((sum, m) => sum + m.quantity, 0),
      totalOutQuantity: movements.filter(m => m.quantity < 0).reduce((sum, m) => sum + Math.abs(m.quantity), 0),
    };

    return {
      movements,
      summary,
      type: 'stock_movements',
    };
  }

  private async getLowStockData(where: any): Promise<any> {
    try {
      const lowStockThreshold = await this.getLowStockThreshold();

      const lowStockWhere = {
        ...where,
        quantityOnHand: { lte: lowStockThreshold },
        isActive: true,
      };

      const items = await this.prisma.inventoryItem.findMany({
        where: lowStockWhere,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              code: true,
              category: true,
              type: true,
            },
          },
          unit: {
            select: {
              id: true,
              name: true,
              abbreviation: true,
            },
          },
          supplier: {
            select: {
              id: true,
              name: true,
              code: true,
              type: true,
            },
          },
        },
        orderBy: { quantityOnHand: 'asc' },
      });

      // Enhanced categorization
      const criticalItems = items.filter(item => item.quantityOnHand === 0);
      const warningItems = items.filter(item => item.quantityOnHand > 0 && item.quantityOnHand <= lowStockThreshold / 2);
      const attentionItems = items.filter(item => item.quantityOnHand > lowStockThreshold / 2 && item.quantityOnHand <= lowStockThreshold);

      // Supplier breakdown analysis
      const supplierBreakdown = items.reduce((acc, item) => {
        const supplierName = item.supplier?.name || 'Unknown';
        if (!acc[supplierName]) {
          acc[supplierName] = {
            supplier: supplierName,
            totalItems: 0,
            criticalItems: 0,
            totalValue: 0,
            averageLeadTime: 7, // Default lead time, could be enhanced with real data
          };
        }
        acc[supplierName].totalItems++;
        if (item.quantityOnHand === 0) {
          acc[supplierName].criticalItems++;
        }
        acc[supplierName].totalValue += item.quantityOnHand * Number(item.costPrice);
        return acc;
      }, {} as Record<string, any>);

      // Location distribution analysis
      const locationDistribution = items.reduce((acc, item) => {
        const location = item.location || 'Unknown';
        if (!acc[location]) {
          acc[location] = { location, count: 0, value: 0 };
        }
        acc[location].count++;
        acc[location].value += item.quantityOnHand * Number(item.costPrice);
        return acc;
      }, {} as Record<string, any>);

      // Generate reorder recommendations
      const reorderRecommendations = this.generateReorderRecommendations(items, lowStockThreshold);

      const summary = {
        totalLowStockItems: items.length,
        criticalItems: criticalItems.length,
        warningItems: warningItems.length,
        attentionItems: attentionItems.length,
        averageStockLevel: items.length > 0 ? items.reduce((sum, item) => sum + item.quantityOnHand, 0) / items.length : 0,
        totalValue: items.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0),
        lowStockThreshold,
        supplierBreakdown: Object.values(supplierBreakdown).sort((a: any, b: any) => b.totalItems - a.totalItems),
        locationDistribution: Object.values(locationDistribution).sort((a: any, b: any) => b.count - a.count),
        reorderRecommendations,
        financialImpact: {
          potentialLostSales: criticalItems.reduce((sum, item) => sum + (Number(item.costPrice) * 30), 0), // Estimated 30 days of lost sales
          reorderCost: items.length * 50000, // Estimated reorder cost per item
          carryingCost: items.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice) * 0.02), 0), // 2% monthly carrying cost
        },
        actionableInsights: this.generateLowStockInsights(criticalItems, warningItems, Object.values(supplierBreakdown)),
      };

      return {
        items,
        criticalItems,
        warningItems,
        attentionItems,
        summary,
        type: 'low_stock',
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error({ err: error }, `Error getting low stock data: ${error.message}`);
      throw new Error(`Gagal mengambil data stok rendah: ${error.message}`);
    }
  }

  private async getExpiryReportData(where: any, filters: ReportFiltersDto): Promise<any> {
    const days = filters.expiringSoonDays || 30;
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    const expiryWhere = {
      ...where,
      expiryDate: { lte: futureDate },
      isActive: true,
    };

    const items = await this.prisma.inventoryItem.findMany({
      where: expiryWhere,
      include: {
        product: true,
        unit: true,
        supplier: true,
      },
      orderBy: { expiryDate: 'asc' },
    });

    const now = new Date();
    const expired = items.filter(item => item.expiryDate && item.expiryDate <= now);
    const expiringSoon = items.filter(item => item.expiryDate && item.expiryDate > now);

    // Categorize by criticality
    const critical = expiringSoon.filter(item => {
      const daysToExpiry = Math.ceil((item.expiryDate!.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysToExpiry <= 7;
    });

    const warning = expiringSoon.filter(item => {
      const daysToExpiry = Math.ceil((item.expiryDate!.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysToExpiry > 7 && daysToExpiry <= 14;
    });

    const attention = expiringSoon.filter(item => {
      const daysToExpiry = Math.ceil((item.expiryDate!.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysToExpiry > 14;
    });

    // Financial impact analysis
    const expiredValue = expired.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0);
    const expiringSoonValue = expiringSoon.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0);
    const criticalValue = critical.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0);

    // Supplier analysis for expired items
    const supplierImpact = expired.reduce((acc, item) => {
      const supplierName = item.supplier?.name || 'Unknown';
      if (!acc[supplierName]) {
        acc[supplierName] = { count: 0, value: 0 };
      }
      acc[supplierName].count++;
      acc[supplierName].value += item.quantityOnHand * Number(item.costPrice);
      return acc;
    }, {} as Record<string, { count: number; value: number }>);

    // Category analysis
    const categoryImpact = expired.reduce((acc, item) => {
      const categoryName = item.product?.category || 'Uncategorized';
      if (!acc[categoryName]) {
        acc[categoryName] = { count: 0, value: 0 };
      }
      acc[categoryName].count++;
      acc[categoryName].value += item.quantityOnHand * Number(item.costPrice);
      return acc;
    }, {} as Record<string, { count: number; value: number }>);

    const summary = {
      totalItems: items.length,
      expiredItems: expired.length,
      expiringSoonItems: expiringSoon.length,
      totalValue: items.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0),
      expiredValue,
      expiringSoonValue,
      criticalityLevels: {
        critical: critical.length,
        warning: warning.length,
        attention: attention.length,
      },
      financialImpact: {
        totalLoss: expiredValue,
        potentialLoss: expiringSoonValue,
        criticalRisk: criticalValue,
        monthlyLossRate: expiredValue, // Could be enhanced with historical data
      },
      supplierBreakdown: Object.entries(supplierImpact)
        .map(([name, data]) => ({ supplier: name, ...data }))
        .sort((a, b) => b.value - a.value),
      categoryBreakdown: Object.entries(categoryImpact)
        .map(([name, data]) => ({ category: name, ...data }))
        .sort((a, b) => b.value - a.value),
      disposalRecommendations: this.generateDisposalRecommendations(expired),
      preventionStrategies: this.generatePreventionStrategies(expiringSoon, critical),
      actionableInsights: this.generateExpiryInsights(expired, expiringSoon, critical),
    };

    return {
      items,
      expired,
      expiringSoon,
      critical,
      warning,
      attention,
      summary,
      type: 'expiry_report',
      generatedAt: new Date().toISOString(),
    };
  }

  private async getAllocationHistoryData(filters: ReportFiltersDto): Promise<any> {
    try {
      // Build date filter for allocations
      const dateFilter: any = {};
      if (filters.startDate || filters.endDate) {
        dateFilter.createdAt = {};
        if (filters.startDate) {
          dateFilter.createdAt.gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          endDate.setHours(23, 59, 59, 999);
          dateFilter.createdAt.lte = endDate;
        }
      }

      // Get stock movements that represent allocations (negative quantities)
      const allocations = await this.prisma.stockMovement.findMany({
        where: {
          ...dateFilter,
          quantity: { lt: 0 }, // Negative quantities represent allocations/consumption
          type: { in: [StockMovementType.ALLOCATION, StockMovementType.OUT] },
        },
        include: {
          inventoryItem: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  code: true,
                  category: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
              supplier: {
                select: {
                  id: true,
                  name: true,
                  code: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      const totalQuantityAllocated = allocations.reduce((sum, allocation) => sum + Math.abs(allocation.quantity), 0);
      const summary = {
        totalAllocations: allocations.length,
        totalQuantityAllocated,
        averageAllocationSize: allocations.length > 0 ? totalQuantityAllocated / allocations.length : 0,
        allocationsByType: allocations.reduce((acc, allocation) => {
          const type = allocation.type || 'UNKNOWN';
          acc[type] = (acc[type] || 0) + Math.abs(allocation.quantity);
          return acc;
        }, {} as Record<string, number>),
        dateRange: {
          startDate: filters.startDate,
          endDate: filters.endDate,
        },
      };

      return {
        allocations: allocations.map(allocation => ({
          id: allocation.id,
          quantity: Math.abs(allocation.quantity),
          type: allocation.type,
          reason: allocation.reason,
          createdAt: allocation.createdAt,
          inventoryItemId: allocation.inventoryItemId,
        })),
        summary,
        type: 'allocation_history',
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error getting allocation history data: ${error.message}`, error.stack);
      throw new Error(`Gagal mengambil data riwayat alokasi: ${error.message}`);
    }
  }

  private async getSupplierPerformanceData(where: any, _filters: ReportFiltersDto): Promise<any> {
    const suppliers = await this.prisma.supplier.findMany({
      include: {
        inventoryItems: {
          where,
          include: {
            product: true,
            unit: true,
          },
        },
      },
    });

    const performanceData = suppliers.map(supplier => {
      const items = supplier.inventoryItems;
      const totalItems = items.length;
      const totalValue = items.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0);
      const lowStockItems = items.filter(item => item.quantityOnHand <= 10).length;
      const expiredItems = items.filter(item => item.expiryDate && item.expiryDate <= new Date()).length;

      return {
        supplier: {
          id: supplier.id,
          name: supplier.name,
          code: supplier.code,
          type: supplier.type,
        },
        metrics: {
          totalItems,
          totalValue,
          lowStockItems,
          expiredItems,
          performanceScore: totalItems > 0 ? ((totalItems - lowStockItems - expiredItems) / totalItems) * 100 : 0,
        },
      };
    });

    const summary = {
      totalSuppliers: suppliers.length,
      averagePerformanceScore: performanceData.length > 0
        ? performanceData.reduce((sum, data) => sum + data.metrics.performanceScore, 0) / performanceData.length
        : 0,
      topPerformer: performanceData.length > 0
        ? performanceData.reduce((prev, current) =>
          prev.metrics.performanceScore > current.metrics.performanceScore ? prev : current
        ).supplier.name
        : 'N/A',
    };

    return {
      suppliers: performanceData,
      summary,
      type: 'supplier_performance',
    };
  }

  private async getCategoryAnalysisData(where: any): Promise<any> {
    // Group by product category/type
    const items = await this.prisma.inventoryItem.findMany({
      where,
      include: {
        product: true,
        unit: true,
      },
    });

    const categoryMap = new Map();

    items.forEach(item => {
      const category = item.product.category || 'Uncategorized';
      if (!categoryMap.has(category)) {
        categoryMap.set(category, {
          category,
          totalItems: 0,
          totalQuantity: 0,
          totalValue: 0,
          lowStockItems: 0,
        });
      }

      const data = categoryMap.get(category);
      data.totalItems++;
      data.totalQuantity += item.quantityOnHand;
      data.totalValue += item.quantityOnHand * Number(item.costPrice);
      if (item.quantityOnHand <= 10) {
        data.lowStockItems++;
      }
    });

    const categories = Array.from(categoryMap.values());

    const summary = {
      totalCategories: categories.length,
      totalItems: items.length,
      totalValue: categories.reduce((sum, cat) => sum + cat.totalValue, 0),
      mostValuableCategory: categories.length > 0
        ? categories.reduce((prev, current) => prev.totalValue > current.totalValue ? prev : current).category
        : 'N/A',
    };

    return {
      categories,
      summary,
      type: 'category_analysis',
    };
  }

  private async getStockAgingData(where: any): Promise<any> {
    const items = await this.prisma.inventoryItem.findMany({
      where,
      include: {
        product: true,
        unit: true,
        supplier: true,
      },
    });

    const now = new Date();
    const agingBuckets: Record<string, {
      days: string,
      items: any,
      totalValue: number
    }> = {
      '0-30': { days: '0-30', items: [], totalValue: 0 },
      '31-60': { days: '31-60', items: [], totalValue: 0 },
      '61-90': { days: '61-90', items: [], totalValue: 0 },
      '91-180': { days: '91-180', items: [], totalValue: 0 },
      '180+': { days: '180+', items: [], totalValue: 0 },
    };

    items.forEach(item => {
      const ageInDays = Math.floor((now.getTime() - new Date(item.receivedDate).getTime()) / (1000 * 60 * 60 * 24));
      const value = item.quantityOnHand * Number(item.costPrice);

      if (ageInDays <= 30) {
        agingBuckets['0-30'].items.push(item);
        agingBuckets['0-30'].totalValue += value;
      } else if (ageInDays <= 60) {
        agingBuckets['31-60'].items.push(item);
        agingBuckets['31-60'].totalValue += value;
      } else if (ageInDays <= 90) {
        agingBuckets['61-90'].items.push(item);
        agingBuckets['61-90'].totalValue += value;
      } else if (ageInDays <= 180) {
        agingBuckets['91-180'].items.push(item);
        agingBuckets['91-180'].totalValue += value;
      } else {
        agingBuckets['180+'].items.push(item);
        agingBuckets['180+'].totalValue += value;
      }
    });

    const summary = {
      totalItems: items.length,
      totalValue: Object.values(agingBuckets).reduce((sum, bucket) => sum + bucket.totalValue, 0),
      oldestItem: items.length > 0
        ? Math.max(...items.map(item => Math.floor((now.getTime() - new Date(item.receivedDate).getTime()) / (1000 * 60 * 60 * 24))))
        : 0,
      averageAge: items.length > 0
        ? items.reduce((sum, item) => sum + Math.floor((now.getTime() - new Date(item.receivedDate).getTime()) / (1000 * 60 * 60 * 24)), 0) / items.length
        : 0,
    };

    return {
      agingBuckets: Object.values(agingBuckets),
      summary,
      type: 'stock_aging',
    };
  }

  private async getTurnoverAnalysisData(where: any, filters: ReportFiltersDto): Promise<any> {
    try {
      // Get date range for analysis (default to last 90 days)
      const endDate = filters.endDate ? new Date(filters.endDate) : new Date();
      const startDate = filters.startDate ? new Date(filters.startDate) : new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);

      // Get inventory items with their movements
      const items = await this.prisma.inventoryItem.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              code: true,
              category: true,
            },
          },
          unit: {
            select: {
              id: true,
              name: true,
              abbreviation: true,
            },
          },
          stockMovements: {
            where: {
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            },
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      const turnoverData = items.map(item => {
        const movements = item.stockMovements;
        const outMovements = movements.filter(m => m.quantity < 0);
        const totalOut = outMovements.reduce((sum, m) => sum + Math.abs(m.quantity), 0);
        const averageStock = item.quantityOnHand; // Simplified - could be improved with historical data

        // Calculate turnover rate (times per period)
        const turnoverRate = averageStock > 0 ? totalOut / averageStock : 0;

        // Calculate days of supply
        const daysInPeriod = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const dailyUsage = totalOut / daysInPeriod;
        const daysOfSupply = dailyUsage > 0 ? item.quantityOnHand / dailyUsage : Infinity;

        return {
          inventoryItem: {
            id: item.id,
            batchNumber: item.batchNumber,
            quantityOnHand: item.quantityOnHand,
            costPrice: item.costPrice,
            product: item.product,
            unit: item.unit,
          },
          metrics: {
            turnoverRate,
            totalOut,
            daysOfSupply: daysOfSupply === Infinity ? 999 : Math.round(daysOfSupply),
            movementCount: outMovements.length,
            averageMovementSize: outMovements.length > 0 ? totalOut / outMovements.length : 0,
          },
          classification: this.classifyTurnover(turnoverRate, daysOfSupply),
        };
      });

      // Sort by turnover rate (highest first)
      turnoverData.sort((a, b) => b.metrics.turnoverRate - a.metrics.turnoverRate);

      const fastMovingItems = turnoverData.filter(item => item.classification === 'FAST').length;
      const slowMovingItems = turnoverData.filter(item => item.classification === 'SLOW').length;
      const averageTurnoverRate = turnoverData.length > 0
        ? turnoverData.reduce((sum, item) => sum + item.metrics.turnoverRate, 0) / turnoverData.length
        : 0;

      const summary = {
        totalItems: turnoverData.length,
        averageTurnoverRate,
        fastMovingItems,
        slowMovingItems,
        normalMovingItems: turnoverData.filter(item => item.classification === 'NORMAL').length,
        deadStockItems: turnoverData.filter(item => item.classification === 'DEAD').length,
        analysisDateRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          daysAnalyzed: Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
        },
      };

      return {
        turnoverData,
        summary,
        type: 'turnover_analysis',
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error getting turnover analysis data: ${error.message}`, error.stack);
      throw new Error(`Gagal mengambil data analisis perputaran: ${error.message}`);
    }
  }

  private classifyTurnover(turnoverRate: number, daysOfSupply: number): string {
    if (turnoverRate >= 4 || daysOfSupply <= 30) {
      return 'FAST'; // Fast moving
    } else if (turnoverRate >= 1 || daysOfSupply <= 90) {
      return 'NORMAL'; // Normal moving
    } else if (turnoverRate > 0 || daysOfSupply <= 180) {
      return 'SLOW'; // Slow moving
    } else {
      return 'DEAD'; // Dead stock
    }
  }

  private generateDisposalRecommendations(expiredItems: any[]): string[] {
    const recommendations: string[] = [];

    if (expiredItems.length === 0) {
      recommendations.push('Tidak ada item kedaluwarsa yang memerlukan pembuangan.');
      return recommendations;
    }

    // High-value items
    const highValueItems = expiredItems.filter(item =>
      (item.quantityOnHand * Number(item.costPrice)) > 1000000
    );
    if (highValueItems.length > 0) {
      recommendations.push(`${highValueItems.length} item bernilai tinggi (>Rp 1jt) memerlukan persetujuan manajemen untuk pembuangan.`);
    }

    // Controlled substances
    const controlledItems = expiredItems.filter(item =>
      item.product?.category?.toLowerCase().includes('narkotika') ||
      item.product?.category?.toLowerCase().includes('psikotropika')
    );
    if (controlledItems.length > 0) {
      recommendations.push(`${controlledItems.length} item terkontrol memerlukan prosedur pembuangan khusus sesuai regulasi.`);
    }

    // Bulk disposal
    if (expiredItems.length > 10) {
      recommendations.push('Pertimbangkan pembuangan massal untuk efisiensi biaya dan waktu.');
    }

    // Documentation
    recommendations.push('Dokumentasikan semua pembuangan untuk audit dan pelaporan regulasi.');

    return recommendations;
  }

  private generatePreventionStrategies(expiringSoon: any[], critical: any[]): string[] {
    const strategies: string[] = [];

    if (critical.length > 0) {
      strategies.push(`URGENT: ${critical.length} item akan kedaluwarsa dalam 7 hari - prioritaskan penjualan atau transfer.`);
    }

    if (expiringSoon.length > 5) {
      strategies.push('Implementasikan sistem FEFO (First Expired, First Out) untuk rotasi stok yang lebih baik.');
    }

    // Supplier-specific strategies
    const supplierCounts = expiringSoon.reduce((acc, item) => {
      const supplier = item.supplier?.name || 'Unknown';
      acc[supplier] = (acc[supplier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const problematicSuppliers = Object.entries(supplierCounts)
      .filter(([_, count]) => (count as number) > 3)
      .map(([supplier, _]) => supplier);

    if (problematicSuppliers.length > 0) {
      strategies.push(`Evaluasi ulang lead time dan minimum order quantity dengan supplier: ${problematicSuppliers.join(', ')}.`);
    }

    strategies.push('Pertimbangkan sistem peringatan otomatis 30-60 hari sebelum kedaluwarsa.');
    strategies.push('Analisis pola pembelian untuk mengurangi over-ordering pada item dengan shelf life pendek.');

    return strategies;
  }

  private generateExpiryInsights(expired: any[], expiringSoon: any[], critical: any[]): string[] {
    const insights: string[] = [];

    const totalExpiredValue = expired.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0);
    const totalCriticalValue = critical.reduce((sum, item) => sum + (item.quantityOnHand * Number(item.costPrice)), 0);

    if (totalExpiredValue > 0) {
      insights.push(`Kerugian finansial dari item kedaluwarsa: Rp ${totalExpiredValue.toLocaleString('id-ID')}.`);
    }

    if (totalCriticalValue > 0) {
      insights.push(`Risiko kerugian dalam 7 hari: Rp ${totalCriticalValue.toLocaleString('id-ID')}.`);
    }

    if (expired.length > 0) {
      const avgDaysExpired = expired.reduce((sum, item) => {
        const daysExpired = Math.ceil((new Date().getTime() - item.expiryDate!.getTime()) / (1000 * 60 * 60 * 24));
        return sum + daysExpired;
      }, 0) / expired.length;

      insights.push(`Rata-rata item sudah kedaluwarsa selama ${Math.round(avgDaysExpired)} hari.`);
    }

    if (expiringSoon.length > expired.length * 2) {
      insights.push('Tren peningkatan item akan kedaluwarsa - perlu tindakan preventif segera.');
    }

    return insights;
  }

  private getReportTypeName(type: InventoryReportType, language: ReportLanguage): string {
    const names = {
      [InventoryReportType.STOCK_LEVELS]: language === ReportLanguage.ID ? 'Tingkat_Stok' : 'Stock_Levels',
      [InventoryReportType.STOCK_MOVEMENTS]: language === ReportLanguage.ID ? 'Pergerakan_Stok' : 'Stock_Movements',
      [InventoryReportType.LOW_STOCK]: language === ReportLanguage.ID ? 'Stok_Rendah' : 'Low_Stock',
      [InventoryReportType.EXPIRY_REPORT]: language === ReportLanguage.ID ? 'Kedaluwarsa' : 'Expiry_Report',
      [InventoryReportType.ALLOCATION_HISTORY]: language === ReportLanguage.ID ? 'Riwayat_Alokasi' : 'Allocation_History',
      [InventoryReportType.SUPPLIER_PERFORMANCE]: language === ReportLanguage.ID ? 'Performa_Supplier' : 'Supplier_Performance',
      [InventoryReportType.CATEGORY_ANALYSIS]: language === ReportLanguage.ID ? 'Analisis_Kategori' : 'Category_Analysis',
      [InventoryReportType.STOCK_AGING]: language === ReportLanguage.ID ? 'Umur_Stok' : 'Stock_Aging',
      [InventoryReportType.TURNOVER_ANALYSIS]: language === ReportLanguage.ID ? 'Analisis_Perputaran' : 'Turnover_Analysis',
    };

    return names[type] || type;
  }

  private async generatePDFReport(request: InventoryReportRequestDto, data: any, reportId: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ size: 'A4', margin: 50 });
        const chunks: Buffer[] = [];

        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);

        // Header
        doc.fontSize(20).text('LAPORAN INVENTORI', { align: 'center' });
        doc.moveDown(0.5);

        // Report info
        doc.fontSize(12);
        doc.text(`Jenis Laporan: ${this.getReportTypeName(request.type, request.language)}`, { align: 'center' });
        doc.text(`ID Laporan: ${reportId}`, { align: 'center' });
        doc.text(`Tanggal Dibuat: ${new Date().toLocaleDateString('id-ID', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })}`, { align: 'center' });

        if (request.userInfo) {
          doc.text(`Dibuat oleh: ${request.userInfo.name} (${request.userInfo.role})`, { align: 'center' });
        }

        if (request.pharmacyInfo) {
          doc.moveDown(0.5);
          doc.text(`${request.pharmacyInfo.name}`, { align: 'center' });
          doc.text(`${request.pharmacyInfo.address}`, { align: 'center' });
          doc.text(`Nomor Izin: ${request.pharmacyInfo.licenseNumber}`, { align: 'center' });
          doc.text(`Apoteker: ${request.pharmacyInfo.pharmacistName}`, { align: 'center' });
        }

        doc.moveDown(1);

        // Add line separator
        doc.moveTo(50, doc.y).lineTo(545, doc.y).stroke();
        doc.moveDown(0.5);

        // Summary section
        if (data.summary && request.includeSummary) {
          doc.fontSize(14).text('RINGKASAN LAPORAN', { underline: true });
          doc.moveDown(0.3);
          doc.fontSize(10);

          for (const [key, value] of Object.entries(data.summary)) {
            const formattedValue = this.formatSummaryValue(key, value);
            doc.text(`${this.formatSummaryKey(key)}: ${formattedValue}`);
          }
          doc.moveDown(1);
        }

        // Charts section (for allocation history reports)
        if (request.includeCharts && request.type === InventoryReportType.ALLOCATION_HISTORY && data.summary) {
          this.addAllocationChartsToPDF(doc, data.summary);
        }

        // Data section (limited for PDF)
        if (data.items && request.includeDetails && data.items.length > 0) {
          doc.fontSize(14).text('DATA DETAIL', { underline: true });
          doc.moveDown(0.3);
          doc.fontSize(8);

          // Table headers
          const headers = ['No', 'Produk', 'Batch', 'Jumlah', 'Satuan', 'Harga', 'Supplier'];
          let y = doc.y;
          let x = 50;
          const colWidths = [30, 120, 80, 50, 50, 70, 100];

          for (let i = 0; i < headers.length; i++) {
            doc.text(headers[i], x, y, { width: colWidths[i], align: 'left' });
            x += colWidths[i];
          }

          doc.moveDown(0.3);
          doc.moveTo(50, doc.y).lineTo(545, doc.y).stroke();
          doc.moveDown(0.2);

          // Table data (limit to first 20 items for PDF)
          const itemsToShow = data.items.slice(0, 20);
          for (let i = 0; i < itemsToShow.length; i++) {
            const item = itemsToShow[i];
            y = doc.y;
            x = 50;

            const rowData = [
              (i + 1).toString(),
              item.product?.name || 'N/A',
              item.batchNumber || 'N/A',
              item.quantityOnHand?.toString() || '0',
              item.unit?.name || 'N/A',
              `Rp ${Number(item.costPrice || 0).toLocaleString('id-ID')}`,
              item.supplier?.name || 'N/A',
            ];

            for (let j = 0; j < rowData.length; j++) {
              doc.text(rowData[j], x, y, { width: colWidths[j], align: 'left' });
              x += colWidths[j];
            }

            doc.moveDown(0.3);

            // Add new page if needed
            if (doc.y > 700) {
              doc.addPage();
              doc.fontSize(8);
            }
          }

          if (data.items.length > 20) {
            doc.moveDown(0.5);
            doc.fontSize(10).text(`... dan ${data.items.length - 20} item lainnya. Untuk data lengkap, gunakan format Excel atau CSV.`, { align: 'center' });
          }
        }

        // Footer
        doc.fontSize(8).text(`Laporan ini dibuat secara otomatis pada ${new Date().toLocaleString('id-ID')}`, 50, 750, { align: 'center' });

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  private formatSummaryKey(key: string): string {
    const keyMap: Record<string, string> = {
      // General metrics
      totalItems: 'Total Item',
      totalQuantity: 'Total Kuantitas',
      totalValue: 'Total Nilai',
      averageValue: 'Rata-rata Nilai',
      activeItems: 'Item Aktif',
      inactiveItems: 'Item Tidak Aktif',

      // Stock level metrics
      lowStockItems: 'Item Stok Rendah',
      totalLowStockItems: 'Total Item Stok Rendah',
      criticalItems: 'Item Kritis',
      warningItems: 'Item Peringatan',
      averageStockLevel: 'Rata-rata Level Stok',
      lowStockThreshold: 'Batas Stok Rendah',
      reorderRecommendations: 'Rekomendasi Pemesanan Ulang',
      supplierBreakdown: 'Breakdown Supplier',
      locationDistribution: 'Distribusi Lokasi',
      stockTurnoverMetrics: 'Metrik Perputaran Stok',

      // Expiry metrics
      expiredItems: 'Item Kedaluwarsa',
      expiringSoonItems: 'Item Akan Kedaluwarsa',
      expiredValue: 'Nilai Item Kedaluwarsa',
      expiringSoonValue: 'Nilai Item Akan Kedaluwarsa',
      financialImpact: 'Dampak Finansial',
      disposalRecommendations: 'Rekomendasi Pembuangan',
      preventionStrategies: 'Strategi Pencegahan',
      criticalityLevels: 'Level Kritikalitas',

      // Movement metrics
      totalMovements: 'Total Pergerakan',
      inMovements: 'Pergerakan Masuk',
      outMovements: 'Pergerakan Keluar',
      totalInQuantity: 'Total Kuantitas Masuk',
      totalOutQuantity: 'Total Kuantitas Keluar',
      movementVelocity: 'Kecepatan Pergerakan',
      trendIndicators: 'Indikator Tren',
      costImpact: 'Dampak Biaya',

      // Supplier metrics
      totalSuppliers: 'Total Supplier',
      averagePerformanceScore: 'Rata-rata Skor Performa',
      topPerformer: 'Supplier Terbaik',
      worstPerformer: 'Supplier Terburuk',
      deliveryReliability: 'Keandalan Pengiriman',
      qualityScores: 'Skor Kualitas',
      costAnalysis: 'Analisis Biaya',
      supplierLeadTimes: 'Lead Time Supplier',

      // Category metrics
      totalCategories: 'Total Kategori',
      mostValuableCategory: 'Kategori Paling Berharga',
      leastValuableCategory: 'Kategori Kurang Berharga',
      profitabilityMetrics: 'Metrik Profitabilitas',
      movementPatterns: 'Pola Pergerakan',
      categoryPerformance: 'Performa Kategori',

      // Turnover metrics
      fastMovingItems: 'Item Cepat Bergerak',
      slowMovingItems: 'Item Lambat Bergerak',
      normalMovingItems: 'Item Pergerakan Normal',
      deadStockItems: 'Item Stok Mati',
      averageTurnoverRate: 'Rata-rata Tingkat Perputaran',
      turnoverEfficiency: 'Efisiensi Perputaran',

      // Allocation metrics
      totalAllocations: 'Total Alokasi',
      totalQuantityAllocated: 'Total Kuantitas Dialokasikan',
      averageAllocationSize: 'Rata-rata Ukuran Alokasi',
      allocationsByType: 'Alokasi Berdasarkan Jenis',
      allocationEfficiency: 'Efisiensi Alokasi',
      allocationPatterns: 'Pola Alokasi',
      optimizationSuggestions: 'Saran Optimisasi',

      // Date and time metrics
      dateRange: 'Rentang Tanggal',
      analysisDateRange: 'Rentang Tanggal Analisis',
      daysAnalyzed: 'Hari Dianalisis',
      reportPeriod: 'Periode Laporan',

      // Aging metrics
      oldestItem: 'Item Tertua',
      averageAge: 'Rata-rata Umur',
      agingBuckets: 'Kelompok Umur',

      // Performance indicators
      kpiSummary: 'Ringkasan KPI',
      benchmarkComparison: 'Perbandingan Benchmark',
      performanceTargets: 'Target Performa',
      actionableInsights: 'Wawasan yang Dapat Ditindaklanjuti',
      recommendations: 'Rekomendasi',
      nextSteps: 'Langkah Selanjutnya',
    };

    return keyMap[key] || key;
  }

  private generateReorderRecommendations(items: any[], lowStockThreshold: number): string[] {
    const recommendations: string[] = [];

    if (items.length === 0) {
      recommendations.push('Tidak ada item yang memerlukan pemesanan ulang saat ini.');
      return recommendations;
    }

    // Critical items (out of stock)
    const criticalItems = items.filter(item => item.quantityOnHand === 0);
    if (criticalItems.length > 0) {
      recommendations.push(`URGENT: ${criticalItems.length} item habis stok - pesan segera untuk menghindari stockout.`);
    }

    // High-value items
    const highValueItems = items.filter(item =>
      (item.quantityOnHand * Number(item.costPrice)) > 500000 && item.quantityOnHand <= lowStockThreshold / 2
    );
    if (highValueItems.length > 0) {
      recommendations.push(`${highValueItems.length} item bernilai tinggi memerlukan perhatian khusus dalam pemesanan.`);
    }

    // Supplier consolidation
    const supplierCounts = items.reduce((acc, item) => {
      const supplier = item.supplier?.name || 'Unknown';
      acc[supplier] = (acc[supplier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const multiItemSuppliers = Object.entries(supplierCounts)
      .filter(([_, count]) => (count as number) > 2)
      .map(([supplier, _]) => supplier);

    if (multiItemSuppliers.length > 0) {
      recommendations.push(`Konsolidasikan pemesanan dari supplier: ${multiItemSuppliers.join(', ')} untuk efisiensi biaya.`);
    }

    // Economic order quantity suggestion
    recommendations.push('Pertimbangkan Economic Order Quantity (EOQ) untuk mengoptimalkan biaya pemesanan dan penyimpanan.');

    return recommendations;
  }

  private generateLowStockInsights(criticalItems: any[], warningItems: any[], supplierBreakdown: any[]): string[] {
    const insights: string[] = [];

    if (criticalItems.length > 0) {
      insights.push(`${criticalItems.length} item dalam status kritis (stok habis) - risiko tinggi kehilangan penjualan.`);
    }

    if (warningItems.length > 0) {
      insights.push(`${warningItems.length} item dalam status peringatan - perlu tindakan dalam 1-2 minggu.`);
    }

    // Supplier analysis
    const problematicSuppliers = supplierBreakdown.filter((s: any) => s.criticalItems > 0);
    if (problematicSuppliers.length > 0) {
      insights.push(`${problematicSuppliers.length} supplier memiliki item kritis - evaluasi performa supplier diperlukan.`);
    }

    // Financial impact
    const totalCriticalValue = criticalItems.reduce((sum, item) => sum + (Number(item.costPrice) * 30), 0);
    if (totalCriticalValue > 1000000) {
      insights.push(`Potensi kehilangan penjualan bulanan: Rp ${totalCriticalValue.toLocaleString('id-ID')}.`);
    }

    // Trend analysis
    const stockoutRatio = criticalItems.length / (criticalItems.length + warningItems.length + 1);
    if (stockoutRatio > 0.3) {
      insights.push('Tingkat stockout tinggi (>30%) - perlu perbaikan sistem perencanaan inventory.');
    }

    return insights;
  }

  private formatFinancialImpactKey(key: string): string {
    const keyMap: Record<string, string> = {
      totalLoss: 'Total Kerugian',
      potentialLoss: 'Potensi Kerugian',
      criticalRisk: 'Risiko Kritis',
      monthlyLossRate: 'Tingkat Kerugian Bulanan',
      potentialLostSales: 'Potensi Kehilangan Penjualan',
      reorderCost: 'Biaya Pemesanan Ulang',
      carryingCost: 'Biaya Penyimpanan',
      disposalCost: 'Biaya Pembuangan',
      opportunityCost: 'Biaya Peluang',
      totalCost: 'Total Biaya',
      savingsPotential: 'Potensi Penghematan',
    };
    return keyMap[key] || key;
  }

  private formatCriticalityKey(key: string): string {
    const keyMap: Record<string, string> = {
      critical: 'Kritis',
      warning: 'Peringatan',
      attention: 'Perhatian',
      normal: 'Normal',
      high: 'Tinggi',
      medium: 'Sedang',
      low: 'Rendah',
      urgent: 'Mendesak',
      moderate: 'Moderat',
      minimal: 'Minimal',
    };
    return keyMap[key] || key;
  }

  private formatSummaryValue(key: string, value: any): string {
    if (value === null || value === undefined) {
      return 'N/A';
    }

    if (typeof value === 'number') {
      // Format currency values
      if (key.toLowerCase().includes('value') || key.toLowerCase().includes('harga') || key.toLowerCase().includes('cost')) {
        return `Rp ${value.toLocaleString('id-ID')}`;
      }
      // Format regular numbers
      return value.toLocaleString('id-ID');
    }

    if (typeof value === 'object') {
      // Handle specific object types
      if (key === 'allocationsByType') {
        // Format allocation types as readable text
        const entries = Object.entries(value as Record<string, number>);
        if (entries.length === 0) {
          return 'Tidak ada data';
        }
        return entries
          .map(([type, quantity]) => `${type}: ${quantity}`)
          .join(', ');
      }

      if (key === 'dateRange' || key === 'analysisDateRange') {
        // Format date range object
        const dateRange = value as { startDate?: string; endDate?: string };
        if (dateRange.startDate && dateRange.endDate) {
          const startDate = new Date(dateRange.startDate).toLocaleDateString('id-ID');
          const endDate = new Date(dateRange.endDate).toLocaleDateString('id-ID');
          return `${startDate} - ${endDate}`;
        } else if (dateRange.startDate) {
          return `Sejak ${new Date(dateRange.startDate).toLocaleDateString('id-ID')}`;
        } else if (dateRange.endDate) {
          return `Sampai ${new Date(dateRange.endDate).toLocaleDateString('id-ID')}`;
        }
        return 'Tidak ditentukan';
      }

      if (key === 'financialImpact') {
        // Format financial impact object with Indonesian labels
        const impact = value as Record<string, number>;
        const entries = Object.entries(impact);
        if (entries.length === 0) {
          return 'Tidak ada data finansial';
        }
        return entries
          .map(([k, v]) => {
            const label = this.formatFinancialImpactKey(k);
            const formattedValue = typeof v === 'number' ? `Rp ${v.toLocaleString('id-ID')}` : v;
            return `${label}: ${formattedValue}`;
          })
          .join(', ');
      }

      if (key === 'criticalityLevels') {
        // Format criticality levels object
        const levels = value as Record<string, number>;
        const entries = Object.entries(levels);
        if (entries.length === 0) {
          return 'Tidak ada data kritikalitas';
        }
        return entries
          .map(([k, v]) => {
            const label = this.formatCriticalityKey(k);
            return `${label}: ${v}`;
          })
          .join(', ');
      }

      if (key === 'supplierBreakdown' || key === 'categoryBreakdown' || key === 'locationDistribution') {
        // Format breakdown arrays
        if (Array.isArray(value)) {
          if (value.length === 0) {
            return 'Tidak ada data breakdown';
          }
          return `${value.length} entri (lihat detail di tabel data)`;
        }
        return 'Format data tidak valid';
      }

      if (key === 'reorderRecommendations' || key === 'disposalRecommendations' || key === 'preventionStrategies' || key === 'actionableInsights') {
        // Format recommendation arrays
        if (Array.isArray(value)) {
          if (value.length === 0) {
            return 'Tidak ada rekomendasi';
          }
          return `${value.length} rekomendasi (lihat detail di bagian rekomendasi)`;
        }
        return 'Format rekomendasi tidak valid';
      }

      // Handle other objects by converting to readable format
      try {
        const entries = Object.entries(value);
        if (entries.length === 0) {
          return 'Tidak ada data';
        }
        return entries
          .map(([k, v]) => {
            // Try to format the key if it's a known field
            const formattedKey = this.formatSummaryKey(k);
            const formattedValue = typeof v === 'number' && (k.toLowerCase().includes('value') || k.toLowerCase().includes('cost'))
              ? `Rp ${v.toLocaleString('id-ID')}`
              : v;
            return `${formattedKey}: ${formattedValue}`;
          })
          .join(', ');
      } catch {
        return 'Data tidak valid';
      }
    }

    if (typeof value === 'boolean') {
      return value ? 'Ya' : 'Tidak';
    }

    // Handle dates
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
      try {
        return new Date(value).toLocaleDateString('id-ID');
      } catch {
        return value;
      }
    }

    return value.toString();
  }

  private addAllocationChartsToPDF(doc: any, summary: any): void {
    try {
      doc.fontSize(14).text('GRAFIK ALOKASI', { underline: true });
      doc.moveDown(0.5);

      // Chart 1: Allocation Types Bar Chart (Text-based)
      if (summary.allocationsByType && typeof summary.allocationsByType === 'object') {
        doc.fontSize(12).text('Alokasi Berdasarkan Jenis:', { underline: true });
        doc.moveDown(0.3);
        doc.fontSize(10);

        const allocations = summary.allocationsByType as Record<string, number>;
        const maxValue = Math.max(...Object.values(allocations));
        const chartWidth = 300;

        Object.entries(allocations).forEach(([type, quantity]) => {
          const barWidth = maxValue > 0 ? (quantity / maxValue) * chartWidth : 0;
          const percentage = maxValue > 0 ? ((quantity / maxValue) * 100).toFixed(1) : '0';

          // Draw text-based bar chart
          doc.text(`${type}:`);
          doc.moveDown(0.1);

          // Create visual bar using characters
          const barLength = Math.max(1, Math.floor(barWidth / 10));
          const bar = '█'.repeat(barLength);
          doc.text(`  ${bar} ${quantity} (${percentage}%)`);
          doc.moveDown(0.3);
        });

        doc.moveDown(0.5);
      }

      // Chart 2: Summary Statistics
      doc.fontSize(12).text('Statistik Ringkasan:', { underline: true });
      doc.moveDown(0.3);
      doc.fontSize(10);

      // Create a simple visual representation of key metrics
      const metrics = [
        { label: 'Total Alokasi', value: summary.totalAllocations || 0 },
        { label: 'Total Kuantitas', value: summary.totalQuantityAllocated || 0 },
        { label: 'Rata-rata Ukuran', value: Math.round(summary.averageAllocationSize || 0) },
      ];

      metrics.forEach(metric => {
        doc.text(`${metric.label}: ${metric.value.toLocaleString('id-ID')}`);
      });

      doc.moveDown(0.5);

      // Chart 3: Date Range Visualization
      if (summary.dateRange) {
        doc.fontSize(12).text('Periode Laporan:', { underline: true });
        doc.moveDown(0.3);
        doc.fontSize(10);

        const dateRange = summary.dateRange;
        if (dateRange.startDate && dateRange.endDate) {
          const startDate = new Date(dateRange.startDate);
          const endDate = new Date(dateRange.endDate);
          const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

          doc.text(`Dari: ${startDate.toLocaleDateString('id-ID')}`);
          doc.text(`Sampai: ${endDate.toLocaleDateString('id-ID')}`);
          doc.text(`Durasi: ${daysDiff} hari`);

          // Simple timeline visualization
          doc.moveDown(0.3);
          doc.text('Timeline:');
          const timeline = '├' + '─'.repeat(20) + '┤';
          doc.text(`${startDate.toLocaleDateString('id-ID')} ${timeline} ${endDate.toLocaleDateString('id-ID')}`);
        }
      }

      doc.moveDown(1);

      // Add note about charts
      doc.fontSize(8)
        .fillColor('gray')
        .text('Catatan: Grafik ditampilkan dalam format teks untuk kompatibilitas PDF', { align: 'center' })
        .fillColor('black');

      doc.moveDown(1);

    } catch (error) {
      this.logger.warn(`Failed to add charts to PDF: ${error.message}`);
      // Continue without charts if there's an error
      doc.fontSize(10).text('Grafik tidak dapat ditampilkan karena error dalam pemrosesan data.');
      doc.moveDown(1);
    }
  }

  private async generateExcelReport(request: InventoryReportRequestDto, data: any, reportId: string): Promise<Buffer> {
    const workbook = XLSX.utils.book_new();

    // Summary sheet
    const summaryData = [
      ['Laporan Inventori'],
      ['Jenis', this.getReportTypeName(request.type, request.language)],
      ['ID Laporan', reportId],
      ['Tanggal', new Date().toLocaleDateString('id-ID')],
      [],
    ];

    if (data.summary) {
      summaryData.push(['Ringkasan']);
      Object.entries(data.summary).forEach(([key, value]) => {
        const formattedKey = this.formatSummaryKey(key);
        const formattedValue = this.formatSummaryValue(key, value);
        summaryData.push([formattedKey, formattedValue]);
      });
    }

    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Ringkasan');

    // Data sheet (if applicable)
    if (data.items && data.items.length > 0) {
      const headers = ['Produk', 'Batch', 'Jumlah', 'Satuan', 'Harga', 'Supplier', 'Lokasi', 'Kedaluwarsa'];
      const itemsData = [headers];

      data.items.forEach((item: any) => {
        itemsData.push([
          item.product?.name || 'N/A',
          item.batchNumber || 'N/A',
          item.quantityOnHand,
          item.unit?.name || 'N/A',
          Number(item.costPrice),
          item.supplier?.name || 'N/A',
          item.location || 'N/A',
          item.expiryDate ? new Date(item.expiryDate).toLocaleDateString('id-ID') : 'N/A',
        ]);
      });

      const itemsSheet = XLSX.utils.aoa_to_sheet(itemsData);
      XLSX.utils.book_append_sheet(workbook, itemsSheet, 'Data');
    }

    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    return Buffer.from(buffer);
  }

  private async generateCSVReport(request: InventoryReportRequestDto, data: any, reportId: string): Promise<Buffer> {
    const csvData: string[] = [];

    // Header
    csvData.push(`# Laporan Inventori`);
    csvData.push(`# Jenis: ${this.getReportTypeName(request.type, request.language)}`);
    csvData.push(`# ID Laporan: ${reportId}`);
    csvData.push(`# Tanggal: ${new Date().toLocaleDateString('id-ID')}`);
    csvData.push('');

    // Summary
    if (data.summary) {
      csvData.push('# Ringkasan');
      Object.entries(data.summary).forEach(([key, value]) => {
        const formattedKey = this.formatSummaryKey(key);
        const formattedValue = this.formatSummaryValue(key, value);
        // Escape commas in values for CSV
        const escapedValue = formattedValue.includes(',') ? `"${formattedValue}"` : formattedValue;
        csvData.push(`${formattedKey},${escapedValue}`);
      });
      csvData.push('');
    }

    // Data
    if (data.items && data.items.length > 0) {
      csvData.push('# Data');
      csvData.push('Produk,Batch,Jumlah,Satuan,Harga,Supplier,Lokasi,Kedaluwarsa');

      data.items.forEach((item: any) => {
        const row = [
          item.product?.name || 'N/A',
          item.batchNumber || 'N/A',
          item.quantityOnHand,
          item.unit?.name || 'N/A',
          Number(item.costPrice),
          item.supplier?.name || 'N/A',
          item.location || 'N/A',
          item.expiryDate ? new Date(item.expiryDate).toLocaleDateString('id-ID') : 'N/A',
        ].map(value => typeof value === 'string' && value.includes(',') ? `"${value}"` : value);

        csvData.push(row.join(','));
      });
    }

    const csvContent = csvData.join('\n');
    return Buffer.from('\ufeff' + csvContent, 'utf8');
  }

  async cleanupExpiredReports(): Promise<void> {
    try {
      if (!fs.existsSync(this.reportsDir)) {
        return;
      }

      const files = fs.readdirSync(this.reportsDir);
      const now = new Date();

      for (const file of files) {
        const filePath = path.join(this.reportsDir, file);
        try {
          const stats = fs.statSync(filePath);
          const fileAge = now.getTime() - stats.mtime.getTime();
          const oneHour = 60 * 60 * 1000;

          if (fileAge > oneHour) {
            fs.unlinkSync(filePath);
            this.logger.info(`Cleaned up expired report: ${file}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to process file ${file}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error during cleanup: ${error.message}`, error.stack);
    }
  }

  async importInventoryData(
    file: Express.Multer.File,
    options: ImportInventoryDto,
    userId: string,
  ): Promise<ImportInventoryResponse> {
    try {
      this.logger.info(`Starting inventory import for user ${userId}, file: ${file.originalname}`);

      // Save uploaded file temporarily
      const tempDir = path.join(process.cwd(), 'temp', 'imports');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFilePath = path.join(tempDir, `${Date.now()}_${file.originalname}`);
      fs.writeFileSync(tempFilePath, file.buffer);

      let importData: any[] = [];
      const errors: string[] = [];
      const warnings: string[] = [];

      try {
        // Parse file based on type
        if (file.mimetype.includes('csv')) {
          importData = await this.parseCSVFile(tempFilePath);
        } else {
          importData = await this.parseExcelFile(tempFilePath);
        }

        // Validate data
        const validationResult = await this.validateImportData(importData);
        errors.push(...validationResult.errors);
        warnings.push(...validationResult.warnings);

        if (options.validateOnly) {
          // Clean up temp file
          fs.unlinkSync(tempFilePath);

          return {
            success: errors.length === 0,
            message: errors.length === 0
              ? `Validasi berhasil. ${importData.length} baris siap diimpor.`
              : `Ditemukan ${errors.length} error dalam validasi.`,
            errors,
            warnings,
          };
        }

        // If validation passed, import the data
        if (errors.length === 0) {
          const importResult = await this.processImportData(importData, userId);

          // Clean up temp file
          fs.unlinkSync(tempFilePath);

          // Handle enhanced error reporting from processImportData
          if (importResult.errors.length > 0 && importResult.imported > 0) {
            const errorMessage = importResult.errors.length > 10
              ? `Import sebagian berhasil. ${importResult.imported} item berhasil diimport, ${importResult.skipped} item dilewati. Menampilkan 10 dari ${importResult.errors.length} error.`
              : `Import sebagian berhasil. ${importResult.imported} item berhasil diimport, ${importResult.skipped} item dilewati.`;

            return {
              success: true,
              message: errorMessage,
              importedCount: importResult.imported,
              skippedCount: importResult.skipped,
              warnings,
              errors: importResult.errors.slice(0, 10),
            };
          }

          if (importResult.errors.length > 0 && importResult.imported === 0) {
            const errorMessage = importResult.errors.length > 10
              ? `Import gagal. Tidak ada item yang berhasil diimport. Menampilkan 10 dari ${importResult.errors.length} error.`
              : `Import gagal. Tidak ada item yang berhasil diimport.`;

            return {
              success: false,
              message: errorMessage,
              importedCount: importResult.imported,
              skippedCount: importResult.skipped,
              warnings,
              errors: importResult.errors.slice(0, 10),
            };
          }

          return {
            success: true,
            message: `Berhasil mengimpor ${importResult.imported} item inventori.`,
            importedCount: importResult.imported,
            skippedCount: importResult.skipped,
            warnings,
          };
        } else {
          // Clean up temp file
          fs.unlinkSync(tempFilePath);

          return {
            success: false,
            message: `Import gagal karena ada ${errors.length} error validasi.`,
            errors,
            warnings,
          };
        }
      } catch (parseError) {
        // Clean up temp file
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
        throw parseError;
      }
    } catch (error) {
      this.logger.error(`Import failed: ${error.message}`, error.stack);
      throw new Error(`Gagal mengimpor data: ${error.message}`);
    }
  }

  async generateImportTemplate(format: string): Promise<{
    buffer: Buffer;
    filename: string;
    contentType: string;
  }> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

      // Get current suppliers and products for dynamic examples
      const [suppliers, products] = await Promise.all([
        this.prisma.supplier.findMany({
          select: { code: true, name: true, type: true },
          orderBy: { name: 'asc' },
          take: 20, // Limit for template size
        }),
        this.prisma.product.findMany({
          select: { code: true, name: true, category: true },
          orderBy: { name: 'asc' },
          take: 20, // Limit for template size
        }),
      ]);

      if (format === 'csv') {
        const csvContent = this.generateCSVTemplate(suppliers, products);
        return {
          buffer: Buffer.from('\ufeff' + csvContent, 'utf8'),
          filename: `Template_Import_Inventori_${timestamp}.csv`,
          contentType: 'text/csv',
        };
      } else {
        // Excel format with enhanced reference sheets
        const workbook = this.generateExcelTemplate(suppliers, products);
        const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

        return {
          buffer: Buffer.from(buffer),
          filename: `Template_Import_Inventori_${timestamp}.xlsx`,
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        };
      }
    } catch (error) {
      this.logger.error(`Failed to generate import template: ${error.message}`, error.stack);
      throw new Error(`Gagal membuat template import: ${error.message}`);
    }
  }

  async getReportProgress(reportId: string): Promise<any> {
    // For now, return a simple progress response
    // In a real implementation, this would track actual progress
    return {
      reportId,
      status: 'completed',
      progress: 100,
      message: 'Laporan selesai dibuat',
    };
  }

  private async parseCSVFile(filePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      try {
        // Check if file exists
        if (!fs.existsSync(filePath)) {
          throw new Error('File CSV tidak ditemukan');
        }

        // Check file size
        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
          throw new Error('File CSV kosong');
        }

        const results: any[] = [];
        const errors: string[] = [];
        let headerRow: string[] = [];
        let rowCount = 0;
        let hasValidData = false;

        // Expected columns for inventory import
        const expectedColumns = [
          'productCode',
          'batchNumber',
          'quantityOnHand',
          'costPrice',
          'supplierCode'
        ];

        const optionalColumns = [
          'sellingPrice',
          'location',
          'expiryDate',
          'receivedDate'
        ];

        // Alternative column names for flexibility
        const alternativeColumns = [
          'productName',
          'supplierName',
          'product',
          'supplier'
        ];

        const allValidColumns = [...expectedColumns, ...optionalColumns, ...alternativeColumns];

        let isFirstRow = true;

        // Create read stream with proper encoding handling
        const stream = fs.createReadStream(filePath, { encoding: 'utf8' })
          .pipe(csv({
            separator: ','
          }));

        stream.on('data', (row) => {
          rowCount++;

          try {
            // Handle header validation on first row
            if (isFirstRow) {
              isFirstRow = false;

              // Get headers from the row keys
              headerRow = Object.keys(row);

              // Remove BOM from first header if present
              if (headerRow.length > 0 && headerRow[0].charCodeAt(0) === 0xFEFF) {
                headerRow[0] = headerRow[0].substring(1);
              }

              // Trim headers
              headerRow = headerRow.map(h => h.trim());

              // Validate headers
              const missingRequired = expectedColumns.filter(col =>
                !headerRow.some(h => h.toLowerCase() === col.toLowerCase())
              );

              if (missingRequired.length > 0) {
                errors.push(`Kolom wajib tidak ditemukan: ${missingRequired.join(', ')}`);
              }

              // Check for unknown columns (warning, not error)
              const unknownColumns = headerRow.filter(h =>
                !allValidColumns.some(col => col.toLowerCase() === h.toLowerCase())
              );

              if (unknownColumns.length > 0) {
                this.logger.warn(`Kolom tidak dikenal akan diabaikan: ${unknownColumns.join(', ')}`);
              }
            }

            // Skip comment lines (starting with #)
            const firstValue = Object.values(row)[0] as string;
            if (firstValue && firstValue.toString().trim().startsWith('#')) {
              return;
            }

            // Check if row has any data
            const hasData = Object.values(row).some(value =>
              value && value.toString().trim() !== ''
            );

            if (!hasData) {
              return; // Skip empty rows
            }

            hasValidData = true;

            // Normalize column names and create clean object
            const normalizedRow: any = {};

            for (const [key, value] of Object.entries(row)) {
              const normalizedKey = allValidColumns.find(col =>
                col.toLowerCase() === key.toLowerCase()
              ) || key;

              normalizedRow[normalizedKey] = value ? value.toString().trim() : '';
            }

            // Validate required fields for this row
            const rowErrors: string[] = [];

            for (const requiredCol of expectedColumns) {
              if (!normalizedRow[requiredCol] || normalizedRow[requiredCol] === '') {
                rowErrors.push(`Baris ${rowCount}: ${requiredCol} wajib diisi`);
              }
            }

            // Validate data types
            if (normalizedRow.quantityOnHand && isNaN(Number(normalizedRow.quantityOnHand))) {
              rowErrors.push(`Baris ${rowCount}: quantityOnHand harus berupa angka`);
            }

            if (normalizedRow.costPrice && isNaN(Number(normalizedRow.costPrice))) {
              rowErrors.push(`Baris ${rowCount}: costPrice harus berupa angka`);
            }

            if (normalizedRow.sellingPrice && normalizedRow.sellingPrice !== '' && isNaN(Number(normalizedRow.sellingPrice))) {
              rowErrors.push(`Baris ${rowCount}: sellingPrice harus berupa angka`);
            }

            // Validate date formats if provided
            if (normalizedRow.expiryDate && normalizedRow.expiryDate !== '') {
              if (!this.isValidDate(normalizedRow.expiryDate)) {
                rowErrors.push(`Baris ${rowCount}: Format expiryDate tidak valid (gunakan YYYY-MM-DD)`);
              }
            }

            if (normalizedRow.receivedDate && normalizedRow.receivedDate !== '') {
              if (!this.isValidDate(normalizedRow.receivedDate)) {
                rowErrors.push(`Baris ${rowCount}: Format receivedDate tidak valid (gunakan YYYY-MM-DD)`);
              }
            }

            // Add row errors to global errors
            errors.push(...rowErrors);

            // Add row to results even if it has errors (for validation reporting)
            results.push(normalizedRow);

          } catch (rowError) {
            errors.push(`Baris ${rowCount}: Error parsing - ${rowError.message}`);
          }
        });

        stream.on('end', () => {
          try {
            // Final validation
            if (!hasValidData) {
              errors.push('File tidak mengandung data yang valid');
            }

            if (results.length === 0) {
              errors.push('Tidak ada data yang dapat diproses');
            }

            if (errors.length > 0) {
              const errorMessage = `Error parsing CSV:\n${errors.slice(0, 10).join('\n')}${errors.length > 10 ? `\n... dan ${errors.length - 10} error lainnya` : ''}`;
              reject(new Error(errorMessage));
              return;
            }

            this.logger.info(`Successfully parsed CSV file: ${results.length} rows processed`);
            resolve(results);

          } catch (finalError) {
            reject(new Error(`Error finalizing CSV parse: ${finalError.message}`));
          }
        });

        stream.on('error', (error) => {
          reject(new Error(`Error reading CSV file: ${error.message}`));
        });

        // Handle timeout for large files
        setTimeout(() => {
          stream.destroy();
          reject(new Error('Timeout parsing CSV file - file mungkin terlalu besar atau format tidak valid'));
        }, 30000); // 30 second timeout

      } catch (error) {
        reject(new Error(`Failed to parse CSV file: ${error.message}`));
      }
    });
  }

  private async parseExcelFile(filePath: string): Promise<any[]> {
    try {
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      return data;
    } catch (error) {
      throw new Error(`Gagal membaca file Excel: ${error.message}`);
    }
  }

  private async validateImportData(data: any[]): Promise<{
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!data || data.length === 0) {
      errors.push('File kosong atau tidak ada data yang dapat dibaca');
      return { errors, warnings };
    }

    // Validate required columns
    const requiredColumns = [
      'productCode',
      'batchNumber',
      'quantityOnHand',
      'costPrice',
      'supplierCode',
    ];

    const firstRow = data[0];
    for (const column of requiredColumns) {
      if (!(column in firstRow)) {
        errors.push(`Kolom wajib '${column}' tidak ditemukan`);
      }
    }

    // Validate each row
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 2; // +2 because Excel rows start at 1 and we have header

      if (!row.productCode) {
        errors.push(`Baris ${rowNum}: Kode produk wajib diisi`);
      }

      if (!row.batchNumber) {
        errors.push(`Baris ${rowNum}: Nomor batch wajib diisi`);
      }

      if (!row.quantityOnHand || isNaN(Number(row.quantityOnHand))) {
        errors.push(`Baris ${rowNum}: Jumlah stok harus berupa angka`);
      }

      if (!row.costPrice || isNaN(Number(row.costPrice))) {
        errors.push(`Baris ${rowNum}: Harga beli harus berupa angka`);
      }

      if (!row.supplierCode) {
        errors.push(`Baris ${rowNum}: Kode supplier wajib diisi`);
      }

      // Validate dates if provided
      if (row.expiryDate && !this.isValidDate(row.expiryDate)) {
        errors.push(`Baris ${rowNum}: Format tanggal kedaluwarsa tidak valid (gunakan YYYY-MM-DD)`);
      }

      if (row.receivedDate && !this.isValidDate(row.receivedDate)) {
        errors.push(`Baris ${rowNum}: Format tanggal terima tidak valid (gunakan YYYY-MM-DD)`);
      }
    }

    return { errors, warnings };
  }

  private async processImportData(data: any[], userId: string): Promise<{
    imported: number;
    skipped: number;
    errors: string[];
  }> {
    let imported = 0;
    let skipped = 0;
    const errors: string[] = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 1;

      try {
        // Find product by code or name (flexible lookup)
        const product = await this.findProductByCodeOrName(row.productCode);
        if (!product) {
          const suggestion = await this.suggestSimilarProducts(row.productCode);
          errors.push(`Baris ${rowNum}: Produk "${row.productCode}" tidak ditemukan.${suggestion}`);
          skipped++;
          continue;
        }

        // Find supplier by code or name (flexible lookup)
        const supplier = await this.findSupplierByCodeOrName(row.supplierCode);
        if (!supplier) {
          const suggestion = await this.suggestSimilarSuppliers(row.supplierCode);
          errors.push(`Baris ${rowNum}: Supplier "${row.supplierCode}" tidak ditemukan.${suggestion}`);
          skipped++;
          continue;
        }

        // Get the product's base unit
        const productWithUnit = await this.prisma.product.findUnique({
          where: { id: product.id },
          include: { baseUnit: true },
        });

        if (!productWithUnit?.baseUnit) {
          skipped++;
          continue;
        }

        // Create inventory item
        await this.prisma.inventoryItem.create({
          data: {
            productId: product.id,
            unitId: productWithUnit.baseUnit.id,
            supplierId: supplier.id,
            batchNumber: row.batchNumber,
            quantityOnHand: Number(row.quantityOnHand),
            costPrice: Number(row.costPrice),
            sellingPrice: row.sellingPrice ? Number(row.sellingPrice) : Number(row.costPrice) * 1.5,
            location: row.location || 'Default',
            expiryDate: row.expiryDate ? new Date(row.expiryDate) : null,
            receivedDate: row.receivedDate ? new Date(row.receivedDate) : new Date(),
            isActive: true,
            createdBy: userId,
            updatedBy: userId,
          },
        });

        imported++;
      } catch (error) {
        this.logger.warn(`Failed to import row: ${error.message}`);
        skipped++;
      }
    }

    return { imported, skipped, errors };
  }

  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Generate CSV template with dynamic supplier and product data
   */
  private generateCSVTemplate(suppliers: any[], products: any[]): string {
    const lines = [
      '# Template Import Inventori - Format CSV',
      '# ==========================================',
      '# PETUNJUK PENGGUNAAN:',
      '# 1. Anda dapat menggunakan KODE atau NAMA untuk supplier dan produk',
      '# 2. Contoh: supplierCode bisa diisi "SUP001" atau "PT Kimia Farma"',
      '# 3. Contoh: productCode bisa diisi "PROD001" atau "Paracetamol 500mg"',
      '# 4. Format tanggal: YYYY-MM-DD (contoh: 2024-12-31)',
      '# 5. Hapus baris komentar ini sebelum upload',
      '#',
      '# KOLOM WAJIB: productCode, batchNumber, quantityOnHand, costPrice, supplierCode',
      '# KOLOM OPSIONAL: sellingPrice, location, expiryDate, receivedDate',
      '#',
    ];

    // Add supplier reference
    if (suppliers.length > 0) {
      lines.push('# DAFTAR SUPPLIER TERSEDIA:');
      lines.push('# Kode | Nama | Tipe');
      suppliers.slice(0, 10).forEach(supplier => {
        lines.push(`# ${supplier.code} | ${supplier.name} | ${supplier.type || 'N/A'}`);
      });
      if (suppliers.length > 10) {
        lines.push(`# ... dan ${suppliers.length - 10} supplier lainnya`);
      }
      lines.push('#');
    }

    // Add product reference
    if (products.length > 0) {
      lines.push('# DAFTAR PRODUK TERSEDIA:');
      lines.push('# Kode | Nama | Kategori');
      products.slice(0, 10).forEach(product => {
        lines.push(`# ${product.code} | ${product.name} | ${product.category || 'N/A'}`);
      });
      if (products.length > 10) {
        lines.push(`# ... dan ${products.length - 10} produk lainnya`);
      }
      lines.push('#');
    }

    // Add header and sample data
    lines.push('productCode,batchNumber,quantityOnHand,costPrice,sellingPrice,supplierCode,location,expiryDate,receivedDate');

    // Add sample data using real data if available
    if (products.length > 0 && suppliers.length > 0) {
      const sampleProduct = products[0];
      const sampleSupplier = suppliers[0];
      lines.push(`${sampleProduct.code},BATCH001,100,5000,7500,${sampleSupplier.code},Gudang A,2024-12-31,2024-01-15`);
      lines.push(`"${sampleProduct.name}",BATCH002,50,10000,15000,"${sampleSupplier.name}",Gudang B,2025-06-30,2024-01-15`);
    } else {
      // Fallback to static examples
      lines.push('PROD001,BATCH001,100,5000,7500,SUP001,Gudang A,2024-12-31,2024-01-15');
      lines.push('"Paracetamol 500mg",BATCH002,50,10000,15000,"PT Kimia Farma",Gudang B,2025-06-30,2024-01-15');
    }

    return lines.join('\n');
  }

  /**
   * Generate Excel template with multiple reference sheets
   */
  private generateExcelTemplate(suppliers: any[], products: any[]): any {
    const workbook = XLSX.utils.book_new();

    // 1. Instructions Sheet
    const instructions = [
      ['Template Import Inventori'],
      [''],
      ['PETUNJUK PENGGUNAAN:'],
      ['1. Isi data pada sheet "Data Import"'],
      ['2. Gunakan sheet "Daftar Supplier" dan "Daftar Produk" sebagai referensi'],
      ['3. Anda dapat menggunakan KODE atau NAMA untuk supplier dan produk'],
      ['4. Format tanggal: YYYY-MM-DD (contoh: 2024-12-31)'],
      ['5. Pastikan semua kolom wajib diisi'],
      ['6. Simpan file dan upload melalui sistem'],
      [''],
      ['KOLOM WAJIB:'],
      ['- productCode: Kode produk ATAU nama produk'],
      ['- batchNumber: Nomor batch unik'],
      ['- quantityOnHand: Jumlah stok (angka)'],
      ['- costPrice: Harga beli (angka)'],
      ['- supplierCode: Kode supplier ATAU nama supplier'],
      [''],
      ['KOLOM OPSIONAL:'],
      ['- sellingPrice: Harga jual (jika kosong, otomatis 150% dari harga beli)'],
      ['- location: Lokasi penyimpanan (default: "Default")'],
      ['- expiryDate: Tanggal kedaluwarsa (YYYY-MM-DD)'],
      ['- receivedDate: Tanggal terima (default: hari ini)'],
      [''],
      ['CONTOH PENGGUNAAN:'],
      ['✓ supplierCode: "SUP001" atau "PT Kimia Farma"'],
      ['✓ productCode: "PROD001" atau "Paracetamol 500mg"'],
      ['✓ Gunakan tanda kutip untuk nama yang mengandung koma'],
    ];

    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions);
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Petunjuk');

    // 2. Supplier Reference Sheet
    if (suppliers.length > 0) {
      const supplierData = [
        ['Kode Supplier', 'Nama Supplier', 'Tipe', 'Keterangan'],
        ...suppliers.map(s => [
          s.code,
          s.name,
          s.type || 'N/A',
          'Gunakan kode atau nama untuk import'
        ])
      ];
      const supplierSheet = XLSX.utils.aoa_to_sheet(supplierData);
      XLSX.utils.book_append_sheet(workbook, supplierSheet, 'Daftar Supplier');
    }

    // 3. Product Reference Sheet
    if (products.length > 0) {
      const productData = [
        ['Kode Produk', 'Nama Produk', 'Kategori', 'Keterangan'],
        ...products.map(p => [
          p.code,
          p.name,
          p.category || 'N/A',
          'Gunakan kode atau nama untuk import'
        ])
      ];
      const productSheet = XLSX.utils.aoa_to_sheet(productData);
      XLSX.utils.book_append_sheet(workbook, productSheet, 'Daftar Produk');
    }

    // 4. Data Import Sheet
    const headers = [
      'productCode',
      'batchNumber',
      'quantityOnHand',
      'costPrice',
      'sellingPrice',
      'supplierCode',
      'location',
      'expiryDate',
      'receivedDate'
    ];

    // Generate sample data using real data if available
    const sampleData: any[][] = [];
    if (products.length > 0 && suppliers.length > 0) {
      const sampleProduct = products[0];
      const sampleSupplier = suppliers[0];

      // Example with codes
      sampleData.push([
        sampleProduct.code, 'BATCH001', 100, 5000, 7500,
        sampleSupplier.code, 'Gudang A', '2024-12-31', '2024-01-15'
      ]);

      // Example with names
      sampleData.push([
        sampleProduct.name, 'BATCH002', 50, 10000, 15000,
        sampleSupplier.name, 'Gudang B', '2025-06-30', '2024-01-15'
      ]);
    } else {
      // Fallback examples
      sampleData.push([
        'PROD001', 'BATCH001', 100, 5000, 7500,
        'SUP001', 'Gudang A', '2024-12-31', '2024-01-15'
      ]);
      sampleData.push([
        'Paracetamol 500mg', 'BATCH002', 50, 10000, 15000,
        'PT Kimia Farma', 'Gudang B', '2025-06-30', '2024-01-15'
      ]);
    }

    const dataSheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
    XLSX.utils.book_append_sheet(workbook, dataSheet, 'Data Import');

    return workbook;
  }

  /**
   * Find product by code or name with flexible matching
   */
  private async findProductByCodeOrName(identifier: string): Promise<any> {
    if (!identifier || identifier.trim() === '') {
      return null;
    }

    const searchTerm = identifier.trim();

    // First try exact code match
    let product = await this.prisma.product.findFirst({
      where: { code: { equals: searchTerm, mode: 'insensitive' } },
      include: { baseUnit: true },
    });

    if (product) return product;

    // Then try exact name match
    product = await this.prisma.product.findFirst({
      where: { name: { equals: searchTerm, mode: 'insensitive' } },
      include: { baseUnit: true },
    });

    if (product) return product;

    // Finally try partial name match
    product = await this.prisma.product.findFirst({
      where: { name: { contains: searchTerm, mode: 'insensitive' } },
      include: { baseUnit: true },
    });

    return product;
  }

  /**
   * Find supplier by code or name with flexible matching
   */
  private async findSupplierByCodeOrName(identifier: string): Promise<any> {
    if (!identifier || identifier.trim() === '') {
      return null;
    }

    const searchTerm = identifier.trim();

    // First try exact code match
    let supplier = await this.prisma.supplier.findFirst({
      where: { code: { equals: searchTerm, mode: 'insensitive' } },
    });

    if (supplier) return supplier;

    // Then try exact name match
    supplier = await this.prisma.supplier.findFirst({
      where: { name: { equals: searchTerm, mode: 'insensitive' } },
    });

    if (supplier) return supplier;

    // Finally try partial name match
    supplier = await this.prisma.supplier.findFirst({
      where: { name: { contains: searchTerm, mode: 'insensitive' } },
    });

    return supplier;
  }

  /**
   * Suggest similar products when exact match fails
   */
  private async suggestSimilarProducts(searchTerm: string): Promise<string> {
    try {
      const suggestions = await this.prisma.product.findMany({
        where: {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { code: { contains: searchTerm, mode: 'insensitive' } },
          ],
        },
        select: { code: true, name: true },
        take: 3,
      });

      if (suggestions.length > 0) {
        const suggestionList = suggestions
          .map(s => `"${s.code}" (${s.name})`)
          .join(', ');
        return ` Mungkin maksud Anda: ${suggestionList}`;
      }

      return ' Pastikan kode atau nama produk sudah benar.';
    } catch (error) {
      return ' Pastikan kode atau nama produk sudah benar.';
    }
  }

  /**
   * Suggest similar suppliers when exact match fails
   */
  private async suggestSimilarSuppliers(searchTerm: string): Promise<string> {
    try {
      const suggestions = await this.prisma.supplier.findMany({
        where: {
          OR: [
            { name: { contains: searchTerm, mode: 'insensitive' } },
            { code: { contains: searchTerm, mode: 'insensitive' } },
          ],
        },
        select: { code: true, name: true },
        take: 3,
      });

      if (suggestions.length > 0) {
        const suggestionList = suggestions
          .map(s => `"${s.code}" (${s.name})`)
          .join(', ');
        return ` Mungkin maksud Anda: ${suggestionList}`;
      }

      return ' Pastikan kode atau nama supplier sudah benar.';
    } catch (error) {
      return ' Pastikan kode atau nama supplier sudah benar.';
    }
  }
}
