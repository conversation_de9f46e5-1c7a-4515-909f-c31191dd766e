import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { StockMovementType } from '@prisma/client';
import { ReferenceType } from '../dto/reference-types.enum';
import { UnitConversionService } from '../../common/services/unit-conversion.service';

export interface ConsumedItem {
  productName: string;
  batchNumber: string;
  quantity: number;
  unit: string;
  movementId: string;
}

@Injectable()
export class StockConsumptionService {
  constructor(
    private prisma: PrismaService,
    private unitConversionService: UnitConversionService,
    @InjectPinoLogger(StockConsumptionService.name)
    private readonly logger: PinoLogger,
  ) { }

  /**
   * Consumes allocated stock for a sale by decreasing the physical stock (quantityOnHand)
   * and creating OUT stock movement records
   *
   * @param saleId - ID of the sale to consume stock for
   * @param userId - ID of the user performing the consumption
   * @param notes - Optional notes for the stock movement
   * @param tx - Optional transaction object to use instead of creating a new one
   * @returns Object with success status and details of consumed stock
   */
  async consumeAllocatedStock(saleId: string, userId: string, notes?: string, tx?: any) {
    this.logger.trace({ saleId, userId }, 'Entering consumeAllocatedStock method');

    this.logger.debug({
      saleId,
      userId,
      hasNotes: !!notes,
      hasTransaction: !!tx
    }, 'Processing stock consumption request');

    try {
      // Use the provided transaction or create a new one
      if (tx) {
        this.logger.debug({ saleId }, 'Using provided transaction for stock consumption');
        const result = await this.processStockConsumption(tx, saleId, userId, notes);
        this.logger.trace({ saleId }, 'Exiting consumeAllocatedStock method (with transaction)');
        return result;
      } else {
        this.logger.debug({ saleId }, 'Creating new transaction for stock consumption');
        // Process the stock consumption in a transaction
        const result = await this.prisma.$transaction(async (txn) => {
          return this.processStockConsumption(txn, saleId, userId, notes);
        });
        this.logger.trace({ saleId }, 'Exiting consumeAllocatedStock method (new transaction)');
        return result;
      }
    } catch (error) {
      this.logger.error({
        err: error,
        saleId,
        userId
      }, 'Failed to consume allocated stock');
      throw error;
    }
  }

  /**
   * Helper method to process stock consumption within a transaction
   */
  private async processStockConsumption(tx: any, saleId: string, userId: string, notes?: string) {
    this.logger.debug({ saleId, userId }, 'Starting stock consumption processing');

    // First, validate the sale exists and is in COMPLETED status
    const sale = await tx.sale.findUnique({
      where: { id: saleId },
      include: {
        saleItems: {
          include: {
            product: true,
            unit: true
          }
        }
      }
    });

    if (!sale) {
      this.logger.warn({ saleId }, 'Sale not found for stock consumption');
      throw new NotFoundException(`Penjualan dengan ID ${saleId} tidak ditemukan`);
    }

    this.logger.debug({
      saleId,
      saleNumber: sale.saleNumber,
      saleStatus: sale.status,
      saleItemsCount: sale.saleItems.length
    }, 'Sale found, validating status');

    if (sale.status !== 'COMPLETED') {
      this.logger.warn({
        saleId,
        saleNumber: sale.saleNumber,
        currentStatus: sale.status,
        requiredStatus: 'COMPLETED'
      }, 'Sale status is not COMPLETED, cannot consume stock');
      throw new BadRequestException(
        `Tidak dapat mengkonsumsi stok untuk penjualan dengan status ${sale.status}. Status harus COMPLETED.`
      );
    }

    this.logger.debug({
      saleId,
      saleNumber: sale.saleNumber,
      saleItemsCount: sale.saleItems.length
    }, 'Sale validation passed, starting stock consumption');

    const consumedItems: ConsumedItem[] = [];

    // Group sale items by product to handle mixed units correctly
    const productGroups = new Map<string, any[]>();
    for (const saleItem of sale.saleItems) {
      if (!productGroups.has(saleItem.productId)) {
        productGroups.set(saleItem.productId, []);
      }
      productGroups.get(saleItem.productId)!.push(saleItem);
    }

    this.logger.debug({
      saleId,
      uniqueProducts: productGroups.size,
      totalSaleItems: sale.saleItems.length
    }, 'Grouped sale items by product');

    // Process each product group
    for (const [productId, productItems] of productGroups) {
      this.logger.debug({
        saleId,
        productId,
        productName: productItems[0].product.name,
        itemsCount: productItems.length
      }, 'Processing product group for stock consumption');

      // Get all inventory items for this product that have allocated stock
      const inventoryItems = await tx.inventoryItem.findMany({
        where: {
          productId: productId,
          quantityAllocated: { gt: 0 },
          isActive: true
        },
        include: {
          product: true,
          unit: true
        },
        orderBy: [
          { expiryDate: 'asc' },
          { receivedDate: 'asc' }
        ]
      });

      this.logger.debug({
        saleId,
        productId,
        productName: productItems[0].product.name,
        allocatedInventoryItems: inventoryItems.length,
        totalAllocatedQuantity: inventoryItems.reduce((sum, item) => sum + item.quantityAllocated, 0)
      }, 'Found inventory items with allocated stock');

      if (inventoryItems.length === 0) {
        this.logger.warn({
          saleId,
          productId,
          productName: productItems[0].product.name
        }, 'No allocated stock found for product');
        throw new BadRequestException(`Tidak ada stok yang dialokasikan untuk produk ${productItems[0].product.name}`);
      }

      // Calculate total demand in base units for this product
      let totalDemandInBaseUnits = 0;
      for (const saleItem of productItems) {
        const conversionResult = await this.unitConversionService.convertToBaseUnit(
          saleItem.productId,
          saleItem.unitId,
          saleItem.quantity
        );

        if (!conversionResult.success) {
          throw new BadRequestException(
            `Gagal mengkonversi unit untuk produk ${saleItem.product.name}: ${conversionResult.error}`
          );
        }

        totalDemandInBaseUnits += conversionResult.convertedQuantity!;
      }

      // Round up to match allocation logic
      const quantityToConsume = Math.ceil(totalDemandInBaseUnits);
      let remainingQuantity = quantityToConsume;

      // Process each inventory item with allocated stock
      for (const inventoryItem of inventoryItems) {
        if (remainingQuantity <= 0) break;

        // Calculate how much to consume from this item
        const quantityToConsume = Math.min(remainingQuantity, inventoryItem.quantityAllocated);

        if (quantityToConsume <= 0) continue;

        // Update inventory item: decrease quantityOnHand and quantityAllocated
        await tx.inventoryItem.update({
          where: { id: inventoryItem.id },
          data: {
            quantityOnHand: { decrement: quantityToConsume },
            quantityAllocated: { decrement: quantityToConsume }
          }
        });

        // Create OUT stock movement
        const stockMovement = await tx.stockMovement.create({
          data: {
            inventoryItemId: inventoryItem.id,
            type: StockMovementType.OUT,
            quantity: -quantityToConsume, // Negative for OUT movements
            unitPrice: inventoryItem.costPrice,
            referenceType: ReferenceType.SALE,
            referenceId: saleId,
            referenceNumber: sale.saleNumber,
            reason: `Pengeluaran stok untuk penjualan #${sale.saleNumber}`,
            notes: notes || `Stok dikonsumsi dari alokasi`,
            movementDate: new Date(),
            createdBy: userId
          }
        });

        consumedItems.push({
          productName: inventoryItem.product.name,
          batchNumber: inventoryItem.batchNumber || 'N/A',
          quantity: quantityToConsume,
          unit: inventoryItem.unit.name,
          movementId: stockMovement.id
        });

        remainingQuantity -= quantityToConsume;
      }

      // If we couldn't consume all the requested quantity
      if (remainingQuantity > 0) {
        this.logger.error({
          saleId,
          productId,
          productName: productItems[0].product.name,
          remainingQuantity,
          totalDemand: quantityToConsume
        }, 'Insufficient allocated stock for product consumption');
        throw new BadRequestException(
          `Stok yang dialokasikan tidak mencukupi untuk ${productItems[0].product.name}. Kekurangan: ${remainingQuantity} unit`
        );
      }

      this.logger.debug({
        saleId,
        productId,
        productName: productItems[0].product.name,
        consumedQuantity: quantityToConsume,
        inventoryItemsProcessed: inventoryItems.length
      }, 'Successfully consumed stock for product');
    }

    this.logger.info({
      saleId,
      saleNumber: sale.saleNumber,
      totalConsumedItems: consumedItems.length,
      uniqueProducts: productGroups.size,
      totalQuantityConsumed: consumedItems.reduce((sum, item) => sum + item.quantity, 0)
    }, 'Stock consumption completed successfully');

    return {
      success: true,
      saleNumber: sale.saleNumber,
      consumedItems,
      message: `Berhasil mengkonsumsi stok untuk penjualan #${sale.saleNumber}`
    };
  }
} 