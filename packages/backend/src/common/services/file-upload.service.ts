import { Injectable } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { existsSync, mkdirSync, unlinkSync } from 'fs';

@Injectable()
export class FileUploadService {
  private readonly uploadPath = './uploads';

  constructor(
    @InjectPinoLogger(FileUploadService.name)
    private readonly logger: PinoLogger,
  ) {
    this.logger.trace('Initializing FileUploadService');

    // Ensure upload directory exists
    if (!existsSync(this.uploadPath)) {
      this.logger.debug({ uploadPath: this.uploadPath }, 'Creating upload directory');
      mkdirSync(this.uploadPath, { recursive: true });
      this.logger.info({ uploadPath: this.uploadPath }, 'Upload directory created');
    } else {
      this.logger.debug({ uploadPath: this.uploadPath }, 'Upload directory already exists');
    }
  }

  getMulterOptions() {
    this.logger.trace('Getting Multer configuration options');

    return {
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = './uploads/supplier-documents';
          this.logger.debug({
            originalname: file.originalname,
            mimetype: file.mimetype,
            uploadPath
          }, 'Processing file upload destination');

          if (!existsSync(uploadPath)) {
            this.logger.debug({ uploadPath }, 'Creating upload destination directory');
            mkdirSync(uploadPath, { recursive: true });
            this.logger.info({ uploadPath }, 'Upload destination directory created');
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          const ext = extname(file.originalname);
          const filename = `${file.fieldname}-${uniqueSuffix}${ext}`;

          this.logger.debug({
            originalname: file.originalname,
            generatedFilename: filename,
            extension: ext,
            fieldname: file.fieldname
          }, 'Generated unique filename for upload');

          cb(null, filename);
        },
      }),
      fileFilter: (req, file, cb) => {
        // Allow common document types
        const allowedMimes = [
          'application/pdf',
          'image/jpeg',
          'image/png',
          'image/jpg',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];

        this.logger.debug({
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        }, 'Validating file type and size');

        if (allowedMimes.includes(file.mimetype)) {
          this.logger.debug({
            originalname: file.originalname,
            mimetype: file.mimetype
          }, 'File type validation passed');
          cb(null, true);
        } else {
          this.logger.warn({
            originalname: file.originalname,
            mimetype: file.mimetype,
            allowedMimes
          }, 'File type validation failed');
          cb(new Error('File type not allowed'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
      },
    };
  }

  deleteFile(filePath: string): boolean {
    this.logger.trace({ filePath }, 'Entering deleteFile method');

    if (!filePath) {
      this.logger.warn('No file path provided for deletion');
      return false;
    }

    try {
      this.logger.debug({ filePath }, 'Attempting to delete file');

      if (existsSync(filePath)) {
        unlinkSync(filePath);
        this.logger.info({ filePath }, 'File deleted successfully');
        return true;
      } else {
        this.logger.warn({ filePath }, 'File not found for deletion');
        return false;
      }
    } catch (error) {
      this.logger.error({
        err: error,
        filePath
      }, 'Error deleting file');
      throw error;
    }
  }

  getSupplierDocumentFileUrl(filename: string): string {
    return `/uploads/supplier-documents/${filename}`;
  }

  getPaymentProofFileUrl(filename: string): string {
    return `/uploads/payment-proofs/${filename}`;
  }
}
