import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { CustomerQueryDto } from './dto/customer-query.dto';
import { CustomerCodeGeneratorService } from './customer-code-generator.service';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class CustomersService {
  constructor(
    private prisma: PrismaService,
    private customerCodeGeneratorService: CustomerCodeGeneratorService,
    @InjectPinoLogger(CustomersService.name)
    private readonly logger: PinoLogger,
  ) { }

  async create(createCustomerDto: CreateCustomerDto, userId: string) {
    this.logger.trace({ userId, customerCode: createCustomerDto.code }, 'Entering create customer method');

    this.logger.debug({
      customerCode: createCustomerDto.code,
      customerName: createCustomerDto.fullName,
      customerType: createCustomerDto.type,
      userId
    }, 'Validating customer data for creation');

    // Generate customer code if not provided
    if (!createCustomerDto.code) {
      this.logger.debug({ customerType: createCustomerDto.type, userId }, 'Generating customer code');
      createCustomerDto.code = await this.customerCodeGeneratorService.generateCustomerCode(
        createCustomerDto.type,
      );
      this.logger.debug({
        generatedCode: createCustomerDto.code,
        customerType: createCustomerDto.type
      }, 'Customer code generated successfully');
    } else {
      // Validate code uniqueness if provided
      this.logger.debug({ customerCode: createCustomerDto.code }, 'Validating customer code uniqueness');
      const isUnique = await this.customerCodeGeneratorService.validateCodeUniqueness(
        createCustomerDto.code,
      );
      if (!isUnique) {
        this.logger.warn({
          customerCode: createCustomerDto.code,
          userId
        }, 'Customer creation failed: code already exists');
        throw new BadRequestException('Kode pelanggan sudah digunakan');
      }
      this.logger.debug({ customerCode: createCustomerDto.code }, 'Customer code uniqueness validated');
    }

    // Validate membership number uniqueness if provided
    if (createCustomerDto.membershipNumber) {
      this.logger.debug({
        membershipNumber: createCustomerDto.membershipNumber
      }, 'Validating membership number uniqueness');

      const existingMembership = await this.prisma.customer.findUnique({
        where: { membershipNumber: createCustomerDto.membershipNumber },
      });
      if (existingMembership) {
        this.logger.warn({
          membershipNumber: createCustomerDto.membershipNumber,
          existingCustomerId: existingMembership.id,
          userId
        }, 'Customer creation failed: membership number already exists');
        throw new BadRequestException('Nomor keanggotaan sudah digunakan');
      }
      this.logger.debug({
        membershipNumber: createCustomerDto.membershipNumber
      }, 'Membership number uniqueness validated');
    }

    this.logger.debug({
      customerCode: createCustomerDto.code,
      customerType: createCustomerDto.type,
      hasMembership: !!createCustomerDto.membershipNumber,
      userId
    }, 'Creating customer with validated data');

    try {
      const customer = await this.prisma.customer.create({
        data: {
          code: createCustomerDto.code!,
          type: createCustomerDto.type,
          firstName: createCustomerDto.firstName,
          lastName: createCustomerDto.lastName,
          fullName: createCustomerDto.fullName,
          phoneNumber: createCustomerDto.phoneNumber,
          email: createCustomerDto.email,
          dateOfBirth: createCustomerDto.dateOfBirth ? new Date(createCustomerDto.dateOfBirth) : undefined,
          gender: createCustomerDto.gender,
          address: createCustomerDto.address,
          city: createCustomerDto.city,
          province: createCustomerDto.province,
          postalCode: createCustomerDto.postalCode,
          membershipNumber: createCustomerDto.membershipNumber,
          membershipLevel: createCustomerDto.membershipLevel,
          loyaltyPoints: createCustomerDto.loyaltyPoints || 0,
          notes: createCustomerDto.notes,
          isActive: createCustomerDto.isActive ?? true,
          createdBy: userId,
          updatedBy: userId,
        },
        include: {
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      this.logger.info({
        customerId: customer.id,
        customerCode: customer.code,
        customerName: customer.fullName,
        customerType: customer.type,
        membershipLevel: customer.membershipLevel,
        loyaltyPoints: customer.loyaltyPoints,
        isActive: customer.isActive,
        userId
      }, 'Customer created successfully');

      this.logger.trace({ customerId: customer.id }, 'Exiting create customer method');
      return customer;
    } catch (error) {
      this.logger.error({
        err: error,
        customerCode: createCustomerDto.code,
        customerName: createCustomerDto.fullName,
        userId
      }, 'Failed to create customer');
      throw error;
    }
  }

  async findAll(query: CustomerQueryDto) {
    this.logger.trace({ query }, 'Entering findAll customers method');

    const { page = 1, limit = 10, search, type, membershipLevel, isActive, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    const skip = (page - 1) * limit;

    this.logger.debug({
      page,
      limit,
      search: search ? '***' : undefined,
      type,
      membershipLevel,
      isActive,
      sortBy,
      sortOrder
    }, 'Processing customer query parameters');

    const where: any = {};

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: 'insensitive' } },
        { phoneNumber: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
      ];
      this.logger.debug({ searchFields: ['fullName', 'phoneNumber', 'email', 'code'] }, 'Applied search filter');
    }

    if (type) {
      where.type = type;
      this.logger.debug({ type }, 'Applied customer type filter');
    }

    if (membershipLevel) {
      where.membershipLevel = membershipLevel;
      this.logger.debug({ membershipLevel }, 'Applied membership level filter');
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
      this.logger.debug({ isActive }, 'Applied active status filter');
    }

    this.logger.debug({
      whereConditions: Object.keys(where),
      orderBy: `${sortBy} ${sortOrder}`,
      pagination: { skip, take: limit }
    }, 'Executing customer query');

    try {
      const startTime = Date.now();
      const [customers, total] = await Promise.all([
        this.prisma.customer.findMany({
          where,
          skip,
          take: limit,
          orderBy: {
            [sortBy as string]: sortOrder,
          },
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            _count: {
              select: {
                sales: true,
              },
            },
          },
        }),
        this.prisma.customer.count({ where }),
      ]);
      const queryTime = Date.now() - startTime;

      const result = {
        data: customers,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPreviousPage: page > 1,
        },
      };

      this.logger.info({
        total,
        returned: customers.length,
        page,
        limit,
        queryTimeMs: queryTime,
        hasFilters: !!(search || type || membershipLevel || isActive !== undefined)
      }, 'Customers retrieved successfully');

      this.logger.trace('Exiting findAll customers method');
      return result;
    } catch (error) {
      this.logger.error({ err: error, query }, 'Failed to retrieve customers');
      throw error;
    }
  }

  async findOne(id: string) {
    this.logger.trace({ customerId: id }, 'Entering findOne customer method');

    try {
      const customer = await this.prisma.customer.findUnique({
        where: { id },
        include: {
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              sales: true,
            },
          },
        },
      });

      if (!customer) {
        this.logger.warn({ customerId: id }, 'Customer not found');
        throw new NotFoundException('Pelanggan tidak ditemukan');
      }

      this.logger.debug({
        customerId: customer.id,
        customerCode: customer.code,
        customerName: customer.fullName,
        customerType: customer.type,
        isActive: customer.isActive,
        salesCount: customer._count.sales
      }, 'Customer retrieved successfully');

      this.logger.trace({ customerId: id }, 'Exiting findOne customer method');
      return customer;
    } catch (error) {
      if (!(error instanceof NotFoundException)) {
        this.logger.error({ err: error, customerId: id }, 'Failed to retrieve customer');
      }
      throw error;
    }
  }

  async update(id: string, updateCustomerDto: UpdateCustomerDto, userId: string) {
    this.logger.trace({ customerId: id, userId }, 'Entering update customer method');

    const customer = await this.findOne(id);

    this.logger.debug({
      customerId: id,
      currentCode: customer.code,
      newCode: updateCustomerDto.code,
      currentMembershipNumber: customer.membershipNumber,
      newMembershipNumber: updateCustomerDto.membershipNumber,
      userId
    }, 'Validating customer update data');

    // Validate code uniqueness if being updated
    if (updateCustomerDto.code && updateCustomerDto.code !== customer.code) {
      this.logger.debug({
        customerId: id,
        newCode: updateCustomerDto.code
      }, 'Validating customer code uniqueness for update');

      const isUnique = await this.customerCodeGeneratorService.validateCodeUniqueness(
        updateCustomerDto.code,
      );
      if (!isUnique) {
        this.logger.warn({
          customerId: id,
          newCode: updateCustomerDto.code,
          userId
        }, 'Customer update failed: code already exists');
        throw new BadRequestException('Kode pelanggan sudah digunakan');
      }
      this.logger.debug({
        customerId: id,
        newCode: updateCustomerDto.code
      }, 'Customer code uniqueness validated for update');
    }

    // Validate membership number uniqueness if being updated
    if (
      updateCustomerDto.membershipNumber &&
      updateCustomerDto.membershipNumber !== customer.membershipNumber
    ) {
      this.logger.debug({
        customerId: id,
        newMembershipNumber: updateCustomerDto.membershipNumber
      }, 'Validating membership number uniqueness for update');

      const existingMembership = await this.prisma.customer.findUnique({
        where: { membershipNumber: updateCustomerDto.membershipNumber },
      });
      if (existingMembership) {
        this.logger.warn({
          customerId: id,
          newMembershipNumber: updateCustomerDto.membershipNumber,
          conflictingCustomerId: existingMembership.id,
          userId
        }, 'Customer update failed: membership number already exists');
        throw new BadRequestException('Nomor keanggotaan sudah digunakan');
      }
      this.logger.debug({
        customerId: id,
        newMembershipNumber: updateCustomerDto.membershipNumber
      }, 'Membership number uniqueness validated for update');
    }

    this.logger.debug({
      customerId: id,
      updateFields: Object.keys(updateCustomerDto),
      userId
    }, 'Starting customer update');

    try {
      const updatedCustomer = await this.prisma.customer.update({
        where: { id },
        data: {
          ...updateCustomerDto,
          updatedBy: userId,
        },
        include: {
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      this.logger.info({
        customerId: id,
        customerCode: updatedCustomer.code,
        customerName: updatedCustomer.fullName,
        updatedFields: Object.keys(updateCustomerDto),
        userId
      }, 'Customer updated successfully');

      this.logger.trace({ customerId: id }, 'Exiting update customer method');
      return updatedCustomer;
    } catch(error) {
      this.logger.error({
        err: error,
        customerId: id,
        updateFields: Object.keys(updateCustomerDto),
        userId
      }, 'Failed to update customer');
      throw error;
    }
  }

  async remove(id: string) {
    this.logger.trace({ customerId: id }, 'Entering remove customer method');

    const customer = await this.findOne(id);

    this.logger.debug({
      customerId: id,
      customerCode: customer.code,
      customerName: customer.fullName
    }, 'Checking customer dependencies before removal');

    // Check if customer has any sales
    const salesCount = await this.prisma.sale.count({
      where: { customerId: id },
    });

    if (salesCount > 0) {
      this.logger.warn({
        customerId: id,
        customerCode: customer.code,
        salesCount
      }, 'Customer removal failed: has transaction history');
      throw new BadRequestException(
        'Tidak dapat menghapus pelanggan yang memiliki riwayat transaksi',
      );
    }

    this.logger.debug({
      customerId: id,
      customerCode: customer.code,
      salesCount
    }, 'Customer dependencies check passed, proceeding with removal');

    try {
      const deletedCustomer = await this.prisma.customer.delete({
        where: { id },
      });

      this.logger.info({
        customerId: id,
        customerCode: customer.code,
        customerName: customer.fullName
      }, 'Customer removed successfully');

      this.logger.trace({ customerId: id }, 'Exiting remove customer method');
      return deletedCustomer;
    } catch (error) {
      this.logger.error({
        err: error,
        customerId: id,
        customerCode: customer.code
      }, 'Failed to remove customer');
      throw error;
    }
  }

  async deactivate(id: string, userId: string) {
    this.logger.trace({ customerId: id, userId }, 'Entering deactivate customer method');

    // Ensure customer exists and get current state
    const customer = await this.findOne(id);

    if (!customer.isActive) {
      this.logger.warn({
        customerId: id,
        customerCode: customer.code,
        userId
      }, 'Customer deactivation failed: already inactive');
      throw new BadRequestException('Pelanggan sudah dalam status tidak aktif');
    }

    this.logger.debug({
      customerId: id,
      customerCode: customer.code,
      customerName: customer.fullName,
      userId
    }, 'Deactivating customer');

    try {
      const deactivatedCustomer = await this.update(id, { isActive: false }, userId);

      this.logger.info({
        customerId: id,
        customerCode: customer.code,
        customerName: customer.fullName,
        userId
      }, 'Customer deactivated successfully');

      this.logger.trace({ customerId: id }, 'Exiting deactivate customer method');
      return deactivatedCustomer;
    } catch(error) {
      this.logger.error({
        err: error,
        customerId: id,
        customerCode: customer.code,
        userId
      }, 'Failed to deactivate customer');
      throw new BadRequestException('Gagal menonaktifkan pelanggan');
    }
  }

  async activate(id: string, userId: string) {
    this.logger.trace({ customerId: id, userId }, 'Entering activate customer method');

    // Ensure customer exists and get current state
    const customer = await this.findOne(id);

    if (customer.isActive) {
      this.logger.warn({
        customerId: id,
        customerCode: customer.code,
        userId
      }, 'Customer activation failed: already active');
      throw new BadRequestException('Pelanggan sudah dalam status aktif');
    }

    this.logger.debug({
      customerId: id,
      customerCode: customer.code,
      customerName: customer.fullName,
      userId
    }, 'Activating customer');

    try {
      const activatedCustomer = await this.update(id, { isActive: true }, userId);

      this.logger.info({
        customerId: id,
        customerCode: customer.code,
        customerName: customer.fullName,
        userId
      }, 'Customer activated successfully');

      this.logger.trace({ customerId: id }, 'Exiting activate customer method');
      return activatedCustomer;
    } catch(error) {
      this.logger.error({
        err: error,
        customerId: id,
        customerCode: customer.code,
        userId
      }, 'Failed to activate customer');
      throw new BadRequestException('Gagal mengaktifkan pelanggan');
    }
  }

  async getStats() {
    this.logger.trace('Entering getStats method');

    try {
      const startTime = Date.now();
      const [total, walkIn, registered, active, inactive] = await Promise.all([
        this.prisma.customer.count(),
        this.prisma.customer.count({ where: { type: 'WALK_IN' } }),
        this.prisma.customer.count({ where: { type: 'REGISTERED' } }),
        this.prisma.customer.count({ where: { isActive: true } }),
        this.prisma.customer.count({ where: { isActive: false } }),
      ]);
      const queryTime = Date.now() - startTime;

      const stats = {
        total,
        walkIn,
        registered,
        active,
        inactive,
      };

      this.logger.info({
        total,
        walkIn,
        registered,
        active,
        inactive,
        queryTimeMs: queryTime
      }, 'Customer statistics retrieved successfully');

      this.logger.trace('Exiting getStats method');
      return stats;
    } catch (error) {
      this.logger.error({ err: error }, 'Failed to retrieve customer statistics');
      throw error;
    }
  }
}
